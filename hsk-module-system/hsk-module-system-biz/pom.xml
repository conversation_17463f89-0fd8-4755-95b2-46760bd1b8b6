<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.xt.hsk</groupId>
        <artifactId>hsk-module-system</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>hsk-module-system-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        system 模块下，我们放通用业务，支撑上层的核心业务。
        例如说：用户、部门、权限、数据字典等等
    </description>

    <dependencies>
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-biz-ip</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-mybatis</artifactId>
        </dependency>

      <!--     oss 用于文件上传 -->
      <dependency>
        <groupId>com.xt.hsk</groupId>
        <artifactId>hsk-spring-boot-starter-oss</artifactId>
      </dependency>

      <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.xt.hsk</groupId>
            <artifactId>hsk-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- 三方云服务相关 -->
        <dependency>
            <groupId>me.zhyd.oauth</groupId>
            <artifactId>JustAuth</artifactId> <!-- 社交登陆（例如说，个人微信、企业微信等等） -->
        </dependency>
        <dependency>
            <groupId>com.xkcoding.justauth</groupId>
            <artifactId>justauth-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-mp-spring-boot-starter</artifactId> <!-- 微信登录（公众号） -->
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>  <!-- 微信登录（小程序） -->
        </dependency>

        <dependency>
            <groupId>com.anji-plus</groupId>
            <artifactId>captcha-spring-boot-starter</artifactId> <!-- 验证码，一般用于登录使用 -->
        </dependency>

        <dependency>
            <groupId>org.dromara.hutool</groupId>
            <artifactId>hutool-extra</artifactId> <!-- 邮件 -->
        </dependency>

      <dependency>
        <groupId>com.dahantc.oss</groupId>
        <artifactId>sms-sdk</artifactId>
        <version>1.0.1-RELEASE</version>
        <scope>system</scope>
        <!--    jar包地址       -->
        <systemPath>${basedir}/src/main/resources/lib/dahan/sms-sdk-1.0.1-RELEASE.jar</systemPath>
      </dependency>
      <dependency>
        <groupId>com.dahantc.oss</groupId>
        <artifactId>oss-common</artifactId>
        <version>1.0.0-RELEASE</version>
        <scope>system</scope>
        <!--    jar包地址       -->
        <systemPath>${basedir}/src/main/resources/lib/dahan/oss-common-1.0.0-RELEASE.jar
        </systemPath>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-servlet</artifactId>
        <version>9.4.9.v20180320</version>
      </dependency>
      <!--    大汉sms需要    -->
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
      </dependency>

    </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <createDependencyReducedPom>false</createDependencyReducedPom>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                  </excludes>
                </filter>
              </filters>
              <artifactSet>
                <includes>
                  <include>com.dahantc.oss:*</include>
                </includes>
              </artifactSet>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

</project>
