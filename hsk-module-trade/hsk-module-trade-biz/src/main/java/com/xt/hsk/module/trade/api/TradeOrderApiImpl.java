package com.xt.hsk.module.trade.api;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xt.hsk.module.trade.enums.ErrorCodeConstants.ORDER_CREATE_VALIDATE_FAILURE;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.xt.hsk.framework.common.enums.CurrencyEnum;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.api.dto.EliteCourseRespDTO;
import com.xt.hsk.module.edu.api.elitecourse.EliteCourseApi;
import com.xt.hsk.module.trade.api.order.TradeOrderApi;
import com.xt.hsk.module.trade.api.order.dto.GiftOrderItemDTO;
import com.xt.hsk.module.trade.api.order.dto.OrderAdminRespDTO;
import com.xt.hsk.module.trade.api.order.dto.UserOrderPageReqDTO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderDO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderItemDO;
import com.xt.hsk.module.trade.dal.dataobject.order.OrderLogDO;
import com.xt.hsk.module.trade.enums.AfterSaleStatusEnum;
import com.xt.hsk.module.trade.enums.OperatorTypeEnum;
import com.xt.hsk.module.trade.enums.OrderLogActionTypeEnum;
import com.xt.hsk.module.trade.enums.OrderSourceEnum;
import com.xt.hsk.module.trade.enums.OrderStatusEnum;
import com.xt.hsk.module.trade.enums.OrderTypeEnum;
import com.xt.hsk.module.trade.enums.PayChannelEnum;
import com.xt.hsk.module.trade.enums.ProductTypeEnum;
import com.xt.hsk.module.trade.service.OrderItemService;
import com.xt.hsk.module.trade.service.OrderLogService;
import com.xt.hsk.module.trade.service.OrderService;
import com.xt.hsk.module.trade.util.BusinessCode;
import com.xt.hsk.module.trade.util.IdGenerator;
import com.xt.hsk.module.trade.validator.AbstractGiftOrderValidator.ValidateResult;
import com.xt.hsk.module.trade.validator.GiftOrderValidatorChain;
import com.xt.hsk.module.user.api.AppUserApi;
import com.xt.hsk.module.user.api.dto.AppUserRespDTO;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 交易订单 API 实现
 *
 * <AUTHOR>
 * @since 2025/06/21
 */
@Service
@Slf4j
public class TradeOrderApiImpl implements TradeOrderApi {

    @Resource
    private OrderItemService orderItemService;

    @Resource
    private OrderService orderService;

    @Resource
    private OrderLogService orderLogService;

    @Resource
    private EliteCourseApi eliteCourseApi;

    @Resource
    private GiftOrderValidatorChain validatorChain;

    @Resource
    private AppUserApi appUserApi;

    /**
     * 根据用户ID查询交易订单
     */
    @Override
    public PageResult<OrderAdminRespDTO> getUserOrderItemList(
        UserOrderPageReqDTO userOrderPageReqDTO) {
        return orderItemService.selectPageOrderItemByUserId(userOrderPageReqDTO);
    }

    /**
     * 创建赠送订单实体
     */
    private OrderDO createGiftOrder(Long userId, AppUserRespDTO user, CurrencyEnum currencyEnum,
        BigDecimal totalAmount) {
        OrderDO orderDO = new OrderDO();
        orderDO.setOrderNo(IdGenerator.generate(BusinessCode.ADMIN_ORDER));
        orderDO.setUserId(userId);
        orderDO.setUserNickname(user.getNickname());
        orderDO.setUserCountryCode(user.getCountryCode());
        orderDO.setUserMobile(user.getMobile());
        orderDO.setCurrency(currencyEnum.getCode());
        orderDO.setProductCount(1);
        orderDO.setPayTime(LocalDateTime.now());
        orderDO.setTotalAmount(totalAmount);
        orderDO.setDiscountAmount(BigDecimal.ZERO);
        orderDO.setPayAmount(BigDecimal.ZERO);
        orderDO.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
        orderDO.setAfterSaleStatus(AfterSaleStatusEnum.NO_AFTER_SALE.getCode());
        orderDO.setCouponReductionAmount(BigDecimal.ZERO);
        orderDO.setOrderSource(OrderSourceEnum.APP.getCode());
        orderDO.setOrderType(OrderTypeEnum.GIFT_ORDER.getCode());
        orderDO.setPayChannel(PayChannelEnum.ADMIN_GIFT.getCode());
        orderDO.setOrderTime(LocalDateTime.now());
        return orderDO;
    }

    /**
     * 选择课程货币和名称 货币选择优先级: 越南盾 > 人民币 > 美元
     */
    private CourseInfo selectCourseInfo(EliteCourseRespDTO course) {
        CourseInfo info = new CourseInfo();
        // 名称快照都取值 价格根据排序优先级取值
        info.setCourseNameEn(course.getCourseNameEn());
        info.setCourseNameOt(course.getCourseNameOt());
        info.setCourseNameCn(course.getCourseNameCn());

        // 越南盾
        if (course.getSellingPriceOt() != null) {
            info.setCurrency(CurrencyEnum.VND);
            info.setTotalAmount(course.getSellingPriceOt());
            return info;
        }
        // 人民币
        else if (course.getSellingPriceCn() != null) {
            info.setCurrency(CurrencyEnum.CNY);
            info.setTotalAmount(course.getSellingPriceCn());
            return info;
        }
        // 美元
        else if (course.getSellingPriceEn() != null) {
            info.setCurrency(CurrencyEnum.USD);
            info.setTotalAmount(course.getSellingPriceEn());
            return info;
        }
        // 默认返回人民币0元
        else {
            info.setCurrency(CurrencyEnum.CNY);
            info.setCourseNameCn(course.getCourseNameCn());
            info.setTotalAmount(BigDecimal.ZERO);
            return info;
        }
    }

    /**
     * 批量赠送精品课程 1.校验参数 2.校验用户和课程信息 3.订单货币获取逻辑 越南盾 > 人民币 > 美元,此处主要看课程中有没有做好配置 4. 创建订单 5. 创建订单项 6.
     * 创建订单日志 7. 返回Map<userID,订单相关参数>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<Long, GiftOrderItemDTO> batchGiftEliteCourse(List<Long> userIds, Long courseId,
        Long operatorId, String operatorIp) {
        log.info("[batchGiftEliteCourse] 开始批量赠送精品课程，用户数量：{}，课程ID：{}",
            userIds.size(), courseId);

        if (CollUtil.isEmpty(userIds)) {
            log.warn("[batchGiftEliteCourse] 用户ID列表为空");
            return Collections.emptyMap();
        }

        // 1. 使用责任链进行校验
        ValidateResult validateResult = validatorChain.getChain().validate(userIds, courseId);
        if (!validateResult.isSuccess()) {
            log.warn("[batchGiftEliteCourse] 校验未通过: {}", validateResult.getErrorMsg());
            throw exception(ORDER_CREATE_VALIDATE_FAILURE, validateResult.getErrorMsg());
        }

        // 查询用户信息 （责任链已校验用户存在，此处直接获取）
        Map<Long, AppUserRespDTO> userMap = appUserApi.getUserList(userIds).stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(AppUserRespDTO::getId, Function.identity()));

        if (userMap.size() != userIds.size()) {
            log.warn("[batchGiftEliteCourse] 部分用户信息获取失败，预期数量：{}，实际数量：{}",
                userIds.size(), userMap.size());
        }

        // 2. 查询课程信息（责任链已校验课程存在，此处直接获取）
        EliteCourseRespDTO course = eliteCourseApi.getById(courseId);
        CourseInfo courseInfo = selectCourseInfo(course);

        // 3. 批量准备订单数据
        List<OrderDO> orderList = new ArrayList<>(userIds.size());
        List<OrderItemDO> orderItemList = new ArrayList<>(userIds.size());
        List<OrderLogDO> orderLogList = new ArrayList<>(userIds.size());
        Map<Long, GiftOrderItemDTO> resultMap = new HashMap<>(userIds.size());

        // 4. 为每个用户创建订单数据
        for (Long userId : userIds) {
            AppUserRespDTO user = userMap.get(userId);
            if (user == null) {
                throw exception(ORDER_CREATE_VALIDATE_FAILURE, "用户ID = {},信息不存在", userId);
            }

            // 创建订单
            OrderDO orderDO = createGiftOrder(userId, user, courseInfo.getCurrency(),
                courseInfo.getTotalAmount());
            orderList.add(orderDO);

            // 预创建订单日志
            OrderLogDO orderLog = buildGiftOrderLog(orderDO.getOrderNo(), userId, operatorId,
                operatorIp, courseId);
            orderLogList.add(orderLog);
        }

        // 5. 批量保存订单
        if (CollUtil.isNotEmpty(orderList)) {
            orderService.saveBatch(orderList);
        }

        // 6. 创建订单明细并与订单关联
        for (int i = 0; i < orderList.size(); i++) {
            OrderDO orderDO = orderList.get(i);
            Long userId = orderDO.getUserId();

            // 创建订单明细
            OrderItemDO orderItem = new OrderItemDO();
            orderItem.setTradeOrderId(orderDO.getId());
            orderItem.setTradeOrderNo(orderDO.getOrderNo());
            orderItem.setUserId(userId);
            orderItem.setProductId(course.getCourseId());
            orderItem.setProductType(ProductTypeEnum.ELITE_COURSE.getCode());
            orderItem.setProductNameCn(courseInfo.getCourseNameCn());
            orderItem.setProductNameEn(courseInfo.getCourseNameEn());
            orderItem.setProductNameOt(courseInfo.getCourseNameOt());
            orderItem.setProductPrice(courseInfo.getTotalAmount());
            orderItem.setQuantity(1);
            orderItem.setCurrency(courseInfo.getCurrency().getCode());
            orderItem.setTotalAmount(courseInfo.getTotalAmount());
            orderItem.setDiscountAmount(BigDecimal.ZERO);
            orderItem.setPayAmount(BigDecimal.ZERO);
            orderItemList.add(orderItem);

            // 构建返回值
            GiftOrderItemDTO itemDTO = new GiftOrderItemDTO();
            itemDTO.setOrderId(orderDO.getId());
            itemDTO.setOrderNo(orderDO.getOrderNo());
            itemDTO.setUserId(userId);
            itemDTO.setCourseId(course.getCourseId());
            itemDTO.setOrderTime(orderDO.getOrderTime());
            resultMap.put(userId, itemDTO);

            // 关联订单日志与订单ID
            orderLogList.get(i).setTradeOrderId(orderDO.getId());
        }

        // 7. 批量保存订单明细
        if (CollUtil.isNotEmpty(orderItemList)) {
            orderItemService.saveBatch(orderItemList);

            // 更新返回值中的订单明细ID
            for (OrderItemDO orderItem : orderItemList) {
                Long userId = orderItem.getUserId();
                GiftOrderItemDTO itemDTO = resultMap.get(userId);
                if (itemDTO != null) {
                    itemDTO.setOrderItemId(orderItem.getId());
                }
            }
        }

        // 8. 批量保存订单日志
        if (CollUtil.isNotEmpty(orderLogList)) {
            // 使用OrderLogService批量保存订单日志
            orderLogService.saveBatch(orderLogList);
        }

        log.info("[batchGiftEliteCourse] 批量赠送精品课程成功，订单数量：{}", orderList.size());
        return resultMap;
    }

    /**
     * 创建订单日志
     */
    private OrderLogDO buildGiftOrderLog(String orderNo, Long userId, Long operatorId,
        String operatorIp, Long courseId) {
        OrderLogDO orderLog = new OrderLogDO();
        orderLog.setTradeOrderNo(orderNo);
        orderLog.setActionType(OrderLogActionTypeEnum.CREATE.getCode());
        orderLog.setBeforeStatus("");
        orderLog.setAfterStatus(OrderStatusEnum.COMPLETED.getDesc());
        orderLog.setIp(operatorIp);
        orderLog.setOperatorId(operatorId);
        orderLog.setOperatorType(OperatorTypeEnum.ADMIN.getCode());
        orderLog.setNotes(
            CharSequenceUtil.format("后台赠送精品课程 课程ID = {}，用户ID = {}", courseId, userId));
        return orderLog;
    }

    /**
     * 课程信息包装类
     */
    @Data
    private static class CourseInfo {

        private CurrencyEnum currency;
        private String courseNameCn;
        private String courseNameEn;
        private String courseNameOt;
        private BigDecimal totalAmount;
    }
}
