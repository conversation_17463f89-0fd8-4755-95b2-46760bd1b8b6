package com.xt.hsk.server;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 项目的启动类
 *
 *
 * <AUTHOR>
 */
@SuppressWarnings("SpringComponentScan") // 忽略 IDEA 无法识别 ${hsk.info.base-package}
@SpringBootApplication(scanBasePackages = {"${hsk.info.base-package}.server", "${hsk.info.base-package}.module"})
public class HskServerApplication {

    public static void main(String[] args) {

        SpringApplication.run(HskServerApplication.class, args);
        // new SpringApplicationBuilder(HskServerApplication.class)
        //     .applicationStartup(new BufferingApplicationStartup(20480))
        //     .run(args);
    }

}
