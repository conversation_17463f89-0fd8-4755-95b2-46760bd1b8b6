package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 精品课-章节 Resp VO
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
public class EliteChapterRespVO {


    /**
     * 章节ID
     */
    private Long id;


    /**
     * 章节名称-中文
     */
    private String chapterNameCn;


    /**
     * 章节名称-英文
     */
    private String chapterNameEn;


    /**
     * 章节名称-其他
     */
    private String chapterNameOt;


    /**
     * 课程id
     */
    private Long courseId;


    /**
     * 排序序号
     */
    private Integer sort;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 章节id（edu_elite_chapter表id）
     */
    private Long chapterId;


}