package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import lombok.Data;

/**
 * 精品课课时学习汇总 RESP VO
 *
 * <AUTHOR>
 * @see 2025-06-07
 */
@Data
public class EliteClassHourStudySummaryRespVO {

    /**
     * 课时 ID
     */
    private Long classHourId;

    /**
     * 课时名称-中文
     */
    private String classHourNameCn;

    /**
     * 课时名称-英文
     */
    private String classHourNameEn;

    /**
     * 课时名称-其他
     */
    private String classHourNameOt;

    /**
     * 学生人数
     */
    private Long studentCount;

    /**
     * 平均学习时长
     */
    private Long avgStudyLength;

    /**
     * 平均学习时长格式化字符串（例如："1小时30分"）
     */
    private String avgStudyLengthStr;


}