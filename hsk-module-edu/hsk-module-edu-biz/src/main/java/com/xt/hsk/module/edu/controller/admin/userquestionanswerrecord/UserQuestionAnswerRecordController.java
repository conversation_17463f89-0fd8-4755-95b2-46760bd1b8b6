package com.xt.hsk.module.edu.controller.admin.userquestionanswerrecord;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.xt.hsk.framework.excel.core.util.ExcelUtils;

import com.xt.hsk.framework.apilog.core.annotation.ApiAccessLog;
import static com.xt.hsk.framework.apilog.core.enums.OperateTypeEnum.*;

import com.xt.hsk.module.edu.controller.admin.userquestionanswerrecord.vo.*;
import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerrecord.UserQuestionAnswerRecordDO;
import com.xt.hsk.module.edu.service.userquestionanswerrecord.UserQuestionAnswerRecordManager;

@Tag(name = "管理后台 - 用户题目作答记录")
@RestController
@RequestMapping("/edu/user-question-answer-record")
@Validated
public class UserQuestionAnswerRecordController {

    @Resource
    private UserQuestionAnswerRecordManager userQuestionAnswerRecordManager;

    @PostMapping("/create")
    @Operation(summary = "创建用户题目作答记录")
    @PreAuthorize("@ss.hasPermission('edu:user-question-answer-record:create')")
    public CommonResult<Long> createUserQuestionAnswerRecord(@Valid @RequestBody UserQuestionAnswerRecordSaveReqVO createReqVO) {
        return success(userQuestionAnswerRecordManager.createUserQuestionAnswerRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户题目作答记录")
    @PreAuthorize("@ss.hasPermission('edu:user-question-answer-record:update')")
    public CommonResult<Boolean> updateUserQuestionAnswerRecord(@Valid @RequestBody UserQuestionAnswerRecordSaveReqVO updateReqVO) {
        userQuestionAnswerRecordManager.updateUserQuestionAnswerRecord(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除用户题目作答记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:user-question-answer-record:delete')")
    public CommonResult<Boolean> deleteUserQuestionAnswerRecord(@RequestParam("id") Long id) {
        userQuestionAnswerRecordManager.deleteUserQuestionAnswerRecord(id);
        return success(true);
    }

    @PostMapping("/get")
    @Operation(summary = "获得用户题目作答记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:user-question-answer-record:query')")
    public CommonResult<UserQuestionAnswerRecordRespVO> getUserQuestionAnswerRecord(@RequestParam("id") Long id) {
        UserQuestionAnswerRecordDO userQuestionAnswerRecord = userQuestionAnswerRecordManager.getUserQuestionAnswerRecord(id);
        return success(BeanUtils.toBean(userQuestionAnswerRecord, UserQuestionAnswerRecordRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得用户题目作答记录分页")
    @PreAuthorize("@ss.hasPermission('edu:user-question-answer-record:query')")
    public CommonResult
            <PageResult
                    <UserQuestionAnswerRecordRespVO>> getUserQuestionAnswerRecordPage(@Valid
                                                                                      @RequestBody UserQuestionAnswerRecordPageReqVO pageReqVO) {
        PageResult<UserQuestionAnswerRecordDO> pageResult = userQuestionAnswerRecordManager.getUserQuestionAnswerRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, UserQuestionAnswerRecordRespVO.class));
    }


}