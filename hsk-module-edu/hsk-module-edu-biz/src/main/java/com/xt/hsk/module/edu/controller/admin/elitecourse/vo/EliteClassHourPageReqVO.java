package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.module.edu.enums.elitecourse.EliteClassHourTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 精品课-课时 req vo
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EliteClassHourPageReqVO extends PageParam {

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 章节id
     */
    private Long chapterId;

    /**
     * 复用id
     */
    private Long reuseId;

    /**
     * 课时名称-中文
     */
    private String classHourNameCn;

    /**
     * 课时名称-英文
     */
    private String classHourNameEn;

    /**
     * 课时名称-其他
     */
    private String classHourNameOt;

    /**
     * 课时类型 1：直播课 2：录播课
     * @see EliteClassHourTypeEnum
     */
    private Integer classHourType;

    /**
     * 视频id
     */
    private Long videoId;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}