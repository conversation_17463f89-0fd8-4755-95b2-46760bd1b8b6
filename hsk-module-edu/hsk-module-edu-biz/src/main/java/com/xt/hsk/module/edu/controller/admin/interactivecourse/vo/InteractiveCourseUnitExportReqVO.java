package com.xt.hsk.module.edu.controller.admin.interactivecourse.vo;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 互动课单元导出请求参数
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InteractiveCourseUnitExportReqVO extends InteractiveCourseUnitPageReqVO {

    /**
     * 导出任务名称
     */
    @NotEmpty(message = "任务名称不能为空")
    private String taskName;

} 