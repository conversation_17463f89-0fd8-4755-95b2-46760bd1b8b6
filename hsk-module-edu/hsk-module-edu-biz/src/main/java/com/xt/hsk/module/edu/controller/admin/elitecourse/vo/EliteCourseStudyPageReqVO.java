package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseLearningStatusEnum;
import com.xt.hsk.module.edu.enums.elitecourse.LearningValidityPeriodEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 精品课学习 req vo
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EliteCourseStudyPageReqVO extends PageParam {

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 学习状态：1.正常 2.已过期
     *
     * @see EliteCourseLearningStatusEnum
     */
    private Integer learningStatus;

    /**
     * 报名途径：1.线上购买 2.线下报名(后台)
     */
    private Integer registerType;

    /**
     * 学习有效期 1：长期有效 2：按截止日期 3：按天数
     *
     * @see LearningValidityPeriodEnum
     */
    private Integer learningValidityPeriod;

    /**
     * 截至日期
     */
    private LocalDateTime deadline;

    /**
     * 有效天数
     */
    private Integer effectiveDays;

    /**
     * ID列表
     */
    private List<Long> ids;

}