package com.xt.hsk.module.edu.controller.admin.textbook.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@AllArgsConstructor
public class UnitTreeResultVO {
    /**
     * id
     */
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 层级
     */
    private Integer deepth;
//    /**
//     * 子级
//     */
//    private List<UnitTreeResultVO> children;

}
