package com.xt.hsk.module.edu.controller.admin.userpracticerecord;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;

import java.util.*;
import java.io.IOException;

import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.xt.hsk.framework.excel.core.util.ExcelUtils;

import com.xt.hsk.framework.apilog.core.annotation.ApiAccessLog;

import static com.xt.hsk.framework.apilog.core.enums.OperateTypeEnum.*;

import com.xt.hsk.module.edu.controller.admin.userpracticerecord.vo.*;
import com.xt.hsk.module.edu.dal.dataobject.userpracticerecord.UserPracticeRecordDO;
import com.xt.hsk.module.edu.service.userpracticerecord.UserPracticeRecordManager;

@Tag(name = "管理后台 - 用户练习记录")
@RestController
@RequestMapping("/edu/user-practice-record")
@Validated
public class UserPracticeRecordController {

    @Resource
    private UserPracticeRecordManager userPracticeRecordManager;

    @PostMapping("/create")
    @Operation(summary = "创建用户练习记录")
    @PreAuthorize("@ss.hasPermission('edu:user-practice-record:create')")
    public CommonResult<Long> createUserPracticeRecord(@Valid @RequestBody UserPracticeRecordSaveReqVO createReqVO) {
        return success(userPracticeRecordManager.createUserPracticeRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户练习记录")
    @PreAuthorize("@ss.hasPermission('edu:user-practice-record:update')")
    public CommonResult<Boolean> updateUserPracticeRecord(@Valid @RequestBody UserPracticeRecordSaveReqVO updateReqVO) {
        userPracticeRecordManager.updateUserPracticeRecord(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除用户练习记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:user-practice-record:delete')")
    public CommonResult<Boolean> deleteUserPracticeRecord(@RequestParam("id") Long id) {
        userPracticeRecordManager.deleteUserPracticeRecord(id);
        return success(true);
    }

    @PostMapping("/get")
    @Operation(summary = "获得用户练习记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:user-practice-record:query')")
    public CommonResult<UserPracticeRecordRespVO> getUserPracticeRecord(@RequestParam("id") Long id) {
        UserPracticeRecordDO userPracticeRecord = userPracticeRecordManager.getUserPracticeRecord(id);
        return success(BeanUtils.toBean(userPracticeRecord, UserPracticeRecordRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得用户练习记录分页")
    @PreAuthorize("@ss.hasPermission('edu:user-practice-record:query')")
    public CommonResult
            <PageResult
                    <UserPracticeRecordRespVO>> getUserPracticeRecordPage(@Valid
                                                                          @RequestBody UserPracticeRecordPageReqVO pageReqVO) {
        PageResult<UserPracticeRecordDO> pageResult = userPracticeRecordManager.getUserPracticeRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, UserPracticeRecordRespVO.class));
    }


}