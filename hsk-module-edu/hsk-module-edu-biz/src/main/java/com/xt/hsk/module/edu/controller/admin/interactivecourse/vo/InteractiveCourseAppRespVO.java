package com.xt.hsk.module.edu.controller.admin.interactivecourse.vo;

import lombok.Data;

/**
 * 互动课程应用程序 Resp VO
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
@Data
public class InteractiveCourseAppRespVO {

    /**
     * 课程ID
     */
    private Long id;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 封面URL
     */
    private String coverUrl;

    /**
     * 单元数量
     */
    private Long unitCount;

    /**
     * 已完成的单元数量
     */
    private Long completedUnitCount;

    /**
     * 是否为最近学习的课程
     */
    private Boolean isRecentlyStudied;
}
