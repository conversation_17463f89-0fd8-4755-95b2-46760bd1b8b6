package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.xt.hsk.module.edu.enums.elitecourse.EliteClassHourReuseStatusEnum;
import lombok.Data;

import java.util.List;

/**
 * 课时复用关系信息类
 *
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
public class EliteClassHourReuseRespVO {
    /**
     * 复用状态：没有复用课时、有复用课时、有被复用课时
     *
     * @see EliteClassHourReuseStatusEnum
     */
    private Integer reuseStatus;

    /**
     * 复用关系列表
     */
    private List<ReuseRelationItem> reuseList;

    /**
     * 复用关系项
     *
     * <AUTHOR>
     * @since 2025-06-11
     */
    @Data
    public static class ReuseRelationItem {
        /**
         * 关联课时ID
         */
        private String classHourId;

        /**
         * 名称（格式：课程名-课时名）
         */
        private String courseClassHourName;
    }
}