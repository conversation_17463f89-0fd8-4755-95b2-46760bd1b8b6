package com.xt.hsk.module.edu.controller.admin.interactivecourse;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.framework.idempotent.core.annotation.Idempotent;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseExportReqVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCoursePageReqVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseRespVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.interactivecourse.InteractiveCourseDO;
import com.xt.hsk.module.edu.manager.interactivecourse.admin.InteractiveCourseManager;
import com.xt.hsk.module.infra.api.export.ExportTaskApi;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 互动课接口
 *
 * <AUTHOR>
 * @since 2025/05/23
 */
@RestController
@RequestMapping("/edu/interactive-course")
@Validated
@Slf4j
public class InteractiveCourseController {

    @Resource
    private InteractiveCourseManager interactiveCourseManager;

    @Resource
    private ExportTaskApi exportTaskApi;

    /**
     * 创建互动课
     */
    @Idempotent()
    @LogRecord(type = LogRecordType.INTERACTIVE_COURSE_TYPE,
        bizNo = "{{#id}}",
        subType = "创建互动课",
        success = "创建了互动课【{{#interactiveCourse.courseNameCn}}】")
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('edu:interactive-course:create')")
    public CommonResult<Long> createInteractiveCourse(@Valid @RequestBody InteractiveCourseSaveReqVO createReqVO) {
        return success(interactiveCourseManager.createInteractiveCourse(createReqVO));
    }


    /**
     * 更新互动课
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('edu:interactive-course:update')")
    public CommonResult<Boolean> updateInteractiveCourse(@Valid @RequestBody InteractiveCourseSaveReqVO updateReqVO) {
        interactiveCourseManager.updateInteractiveCourse(updateReqVO);
        return success(true);
    }


    /**
     * 删除互动课
     * @param id 互动课ID
     */
    @LogRecord(type = LogRecordType.INTERACTIVE_COURSE_TYPE,
        bizNo = "{{#id}}",
        success = "删除了名为【{{#interactiveCourse.courseNameCn}}】的互动课",
        subType = "删除互动课")
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('edu:interactive-course:delete')")
    public CommonResult<Boolean> deleteInteractiveCourse(@RequestParam("id") Long id) {
        interactiveCourseManager.deleteInteractiveCourse(id);
        return success(true);
    }


    /**
     * 获得互动课
     * @param id 互动课ID
     */
    @GetMapping("/get")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('edu:interactive-course:query')")
    public CommonResult<InteractiveCourseRespVO> getInteractiveCourse(@RequestParam("id") Long id) {
        InteractiveCourseDO interactiveCourse = interactiveCourseManager.getInteractiveCourse(id);
        return success(BeanUtils.toBean(interactiveCourse, InteractiveCourseRespVO.class));
    }


    /**
     * 获得互动课分页
     */
    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('edu:interactive-course:query')")
    public CommonResult<PageResult<InteractiveCourseRespVO>> getInteractiveCoursePage(
        @RequestBody @Valid InteractiveCoursePageReqVO pageReqVO) {
        return success(interactiveCourseManager.getInteractiveCourseVoPage(pageReqVO));
    }

    /**
     * 隐藏或者显示
     * @param id 互动课ID
     */
    @PutMapping("/update-status")
    @PreAuthorize("@ss.hasPermission('edu:interactive-course:update')")
    public CommonResult<Boolean> updateStatus(@RequestParam("id") Long id) {
        interactiveCourseManager.updateStatus(id);
        return success(true);
    }

    /**
     * 修改排序
     * @param id 互动课ID
     * @param sort 排序值
     */
    @PutMapping("/update-sort")
    @PreAuthorize("@ss.hasPermission('edu:interactive-course:update')")
    public CommonResult<Boolean> updateSort(@RequestParam("id") Long id, @RequestParam("sort") Integer sort) {
        interactiveCourseManager.updateSort(id, sort);
        return success(true);
    }

    /**
     * 导出互动课数据
     */
    @LogRecord(type = LogRecordType.INTERACTIVE_COURSE_TYPE, bizNo = "{{#taskId}}", success = "创建互动课异步导出任务,任务名称【{{taskName}}】")
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermission('edu:interactive-course:export')")
    public CommonResult<Long> exportInteractiveCourse(
        @RequestBody @Valid InteractiveCourseExportReqVO exportReqVO) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 将导出请求参数和任务名称一起传递
            ExportTaskParams taskParams = new ExportTaskParams();
            taskParams.setTaskName(exportReqVO.getTaskName());
            taskParams.setQueryParams(exportReqVO);

            String params = objectMapper.writeValueAsString(taskParams);
            Long taskId = exportTaskApi.createExportTask(exportReqVO.getTaskName(),
                ExportTaskTypeEnum.INTERACTIVE_COURSE, params);
            LogRecordContext.putVariable("taskId", taskId);
            LogRecordContext.putVariable("taskName", exportReqVO.getTaskName());
            return success(taskId);
        } catch (Exception e) {
            log.error("创建互动课导出任务失败", e);
            return CommonResult.error(500, "创建导出任务失败：" + e.getMessage());
        }
    }

}