package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 精品课讲师关联 分页 req vo
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EliteCourseTeacherPageReqVO extends PageParam {

    /**
     * 讲师id（edu_teacher表id）
     */
    private Long teacherId;

    /**
     * 精品课程id（edu_elite_course表id）
     */
    private Long eliteCourseId;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}