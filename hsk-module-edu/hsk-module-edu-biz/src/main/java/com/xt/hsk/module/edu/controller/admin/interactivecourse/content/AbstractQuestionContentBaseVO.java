package com.xt.hsk.module.edu.controller.admin.interactivecourse.content;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 题目内容基础类
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "contentType")
@JsonSubTypes({
    @JsonSubTypes.Type(value = SpecialPracticeContentVOAbstract.class, name = "SPECIAL_PRACTICE"),
    @JsonSubTypes.Type(value = RealExamContentVOAbstract.class, name = "REAL_EXAM"),
    @JsonSubTypes.Type(value = LocalVideoContentVOAbstract.class, name = "LOCAL_VIDEO")
})
public abstract class AbstractQuestionContentBaseVO {

    /**
     * 题目来源类型
     * 1-专项练习 2-真题练习 3-本地视频
     */
    @NotNull(message = "题目来源类型不能为空")
    private Integer type;

} 