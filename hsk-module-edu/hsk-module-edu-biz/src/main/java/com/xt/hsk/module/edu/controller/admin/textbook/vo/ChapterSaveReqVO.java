package com.xt.hsk.module.edu.controller.admin.textbook.vo;

import lombok.*;

import jakarta.validation.constraints.*;

import java.util.List;

@Data
public class ChapterSaveReqVO {

    /**
     * 章节id
     */
    private Long id;

    /**
     * 教材id
     */
    @NotNull(message = "教材id不能为空")
    private Long textbookId;

    /**
     * 科目（1=听力，2=阅读，4=书写）
     */

    private Integer subject;
    /**
     * 科目集合
     */
    @NotNull(message = "科目不能为空")
    private List<Integer> subjects;

    /**
     * 章节名称
     */
    @NotEmpty(message = "章节名称不能为空")
    private String chapterNameCn;

    /**
     * 章节名称 英文
     */
    private String chapterNameEn;

    /**
     * 章节名称 其他
     */
    private String chapterNameOt;
    /**
     * 真题数量
     */
    private Integer questionNumber;


    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;

    /**
     * 章节序号
     */
    @NotNull(message = "章节序号不能为空")
    private Integer chapterOrder;

    /**
     * 状态 0开启 1关闭
     */
    private Integer status;

    /**
     * 单元
     */
    private List<UnitSaveReqVO> unitList;

}