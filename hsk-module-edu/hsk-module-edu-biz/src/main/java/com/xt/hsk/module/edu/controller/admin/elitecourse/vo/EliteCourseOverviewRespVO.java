package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import lombok.Data;

/**
 * 精品课概览 Resp VO
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
public class EliteCourseOverviewRespVO {

    /**
     * 精品课ID
     */
    private Long courseId;

    /**
     * 课程封面大图URL
     */
    private String coverUrlLarge;

    /**
     * 课程封面小图URL
     */
    private String coverUrlSmall;

    /**
     * 课程名称-中文
     */
    private String courseNameCn;

    /**
     * 课程名称-英文
     */
    private String courseNameEn;

    /**
     * 课程名称-其他
     */
    private String courseNameOt;

    /**
     * 总章节数
     */
    private Long chapterCount;

    /**
     * 总课时数
     */
    private Long classHourCount;

    /**
     * 学习总人数
     */
    private Long totalStudentCount;

    /**
     * 已过期人数
     */
    private Long expiredStudentCount;

    /**
     * 录播课数量
     */
    private Long recordedClassCount;

} 