package com.xt.hsk.module.edu.controller.admin.question;

import lombok.Data;

import java.util.List;


/**
 * 操作基本状态类
 */
@Data
public class QuestionCommonStatusVO {

    /**
     * 主键
     */
    private Long id;
    /**
     * ids
     */
    private List<Long> ids;


    /**
     * 状态 0开启 1关闭
     */
    private Integer status;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    // 删除字段
    private Boolean deleted;

}