package com.xt.hsk.module.edu.controller.admin.interactivecourse.content;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 真题练习题目内容
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RealExamContentVOAbstract extends AbstractQuestionContentBaseVO {

    /**
     * 真题练习ID
     */
    @NotNull(message = "真题练习ID不能为空")
    private Long realExamId;

    /**
     * 版本号
     */
    @NotNull(message = "版本号不能为空")
    private Integer version;

} 