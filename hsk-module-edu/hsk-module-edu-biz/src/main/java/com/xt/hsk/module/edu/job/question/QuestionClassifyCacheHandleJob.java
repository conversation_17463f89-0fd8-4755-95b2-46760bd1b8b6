package com.xt.hsk.module.edu.job.question;

import com.baomidou.lock.annotation.Lock4j;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.quartz.core.handler.JobHandler;
import com.xt.hsk.framework.redis.utils.RedisUtil;
import com.xt.hsk.module.edu.service.question.QuestionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 题目分类缓存处理任务
 * <p>
 * 功能：为每个HSK等级和科目组合缓存随机题目ID列表
 * 等级：HSK1(1), HSK2(2), HSK3(4), HSK4(8), HSK5(16), HSK6(32)
 * 科目：听力(1), 阅读(2), 书写(4)
 * 注意：HSK1、HSK2、HSK6 没有书写题
 */
@Component
@Slf4j
public class QuestionClassifyCacheHandleJob implements JobHandler {

    /**
     * Redis缓存Key模板：HSK:question:classify:level:subject
     */
    private static final String CACHE_KEY_TEMPLATE = "HSK:question:classify:%d:%d";

    /**
     * 每次处理的题目数量
     */
    private static final int BATCH_SIZE = 200;

    /**
     * 缓存过期时间（天）
     */
    private static final int CACHE_EXPIRE_DAYS = 1;

    /**
     * HSK等级配置：对应位运算值
     */
    private static final int[] HSK_LEVELS = {1, 2, 4, 8, 16, 32};

    /**
     * 科目配置：对应位运算值
     */
    private static final int[] SUBJECTS = {1, 2, 4};

    /**
     * 没有书写题的HSK等级索引
     */
    private static final Set<Integer> NO_WRITING_LEVELS = Set.of(0, 1, 5); // HSK1, HSK2, HSK6

    @Resource
    private QuestionService questionService;

    @Resource
    private RedisUtil redisUtil;

    @Override
    @Lock4j(name = "question_classify_cache_handle_job")
    public String execute(String param) {
        long startTime = System.currentTimeMillis();
        log.info("题目分类缓存处理任务开始执行，参数：{}", param);

        try {
            // 1. 获取题目数据
            Map<String, List<Long>> questionMap = fetchQuestionData();

            // 2. 更新Redis缓存
            updateRedisCache(questionMap);

            long endTime = System.currentTimeMillis();
            String result = String.format("题目分类缓存处理任务执行完成，处理了%d个缓存项，耗时%dms",
                    questionMap.size(), endTime - startTime);
            log.info(result);
            return result;

        } catch (Exception e) {
            log.error("题目分类缓存处理任务执行失败", e);
            throw e;
        }
    }

    /**
     * 获取题目数据
     */
    private Map<String, List<Long>> fetchQuestionData() {
        Map<String, List<Long>> questionMap = new HashMap<>();

        // 遍历所有HSK等级
        for (int levelIndex = 0; levelIndex < HSK_LEVELS.length; levelIndex++) {
            int hskLevel = HSK_LEVELS[levelIndex];

            // 遍历所有科目
            for (int subjectIndex = 0; subjectIndex < SUBJECTS.length; subjectIndex++) {
                int subject = SUBJECTS[subjectIndex];

                // 跳过没有书写题的等级
                if (subject == SubjectEnum.WRITING.code && NO_WRITING_LEVELS.contains(levelIndex)) {
                    log.debug("跳过HSK{}的书写题（该等级无书写题）", hskLevel);
                    continue;
                }

                try {
                    log.info("开始处理HSK{}等级，科目{}的题目", hskLevel, SubjectEnum.getByCode(subjectIndex));

                    List<Long> questionIds = questionService.randomQuestionIds(hskLevel, subject, BATCH_SIZE);

                    if (questionIds != null && !questionIds.isEmpty()) {
                        String cacheKey = String.format(CACHE_KEY_TEMPLATE, levelIndex, subjectIndex);
                        questionMap.put(cacheKey, questionIds);
                        log.info("HSK{}等级，科目{}获取到{}道题目", hskLevel, SubjectEnum.getByCode(subjectIndex), questionIds.size());
                    } else {
                        log.warn("HSK{}等级，科目{}未获取到题目", hskLevel, SubjectEnum.getByCode(subjectIndex));
                    }

                } catch (Exception e) {
                    log.error("获取HSK{}等级，科目{}的题目失败", hskLevel, SubjectEnum.getByCode(subjectIndex), e);
                }
            }
        }

        log.info("题目数据获取完成，共获取{}个缓存项", questionMap.size());
        return questionMap;
    }

    /**
     * 更新Redis缓存
     */
    private void updateRedisCache(Map<String, List<Long>> questionMap) {
        if (questionMap.isEmpty()) {
            log.warn("没有题目数据需要缓存");
            return;
        }

        log.info("开始更新Redis缓存，共{}个缓存项", questionMap.size());

        // 使用并行流提高性能
        List<CompletableFuture<Void>> futures = questionMap.entrySet().parallelStream()
                .map(entry -> CompletableFuture.runAsync(() -> updateSingleCache(entry.getKey(), entry.getValue())))
                .toList();

        // 等待所有缓存更新完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        log.info("Redis缓存更新完成");
    }

    /**
     * 更新单个缓存项
     */
    private void updateSingleCache(String cacheKey, List<Long> questionIds) {
        try {
            log.debug("更新缓存：{}, 题目数量：{}", cacheKey, questionIds.size());

            // 删除旧缓存
            redisUtil.delete(cacheKey);

            // 添加新数据
            redisUtil.sAdd(cacheKey, questionIds);

            // 设置过期时间
            redisUtil.expire(cacheKey, CACHE_EXPIRE_DAYS, TimeUnit.DAYS);

            log.debug("缓存更新成功：{}", cacheKey);

        } catch (Exception e) {
            log.error("更新缓存失败：{}", cacheKey, e);
        }
    }
}
