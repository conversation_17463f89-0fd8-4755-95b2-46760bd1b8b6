package com.xt.hsk.module.edu.controller.admin.textbook.vo;

import com.xt.hsk.framework.common.enums.SubjectEnum;
import lombok.*;

import java.util.*;

import java.time.LocalDateTime;

@Data
public class ChapterRespVO {


    /**
     * 章节id
     */
    private Long id;


    /**
     * 教材id
     */
    private Long textbookId;


    /**
     * 科目（1=听力，2=阅读，4=书写）
     */
    private Integer subject;
    /**
     * 科目数组
     */
    private List<Integer> subjectList;

    /**
     * 章节名称
     */
    private String chapterNameCn;


    /**
     * 章节名称 英文
     */
    private String chapterNameEn;


    /**
     * 章节名称 其他
     */
    private String chapterNameOt;


    /**
     * 章节序号
     */
    private Integer chapterOrder;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 真题数量
     */
    private Integer questionNumber;


    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;


    /**
     * 状态 0开启 1关闭
     */
    private Integer status;

    /**
     * 单元详情
     */
    private List<UnitRespVO> unitList;
    /**
     * 单元id
     */
    private Long unitId;
    /**
     * 单元名称
     */
    private String unitName;
    /**
     * 单元科目
     */
    private Integer unitSubject;
    /**
     * 单元科目描述
     */
    private String unitSubjectDesc;

}