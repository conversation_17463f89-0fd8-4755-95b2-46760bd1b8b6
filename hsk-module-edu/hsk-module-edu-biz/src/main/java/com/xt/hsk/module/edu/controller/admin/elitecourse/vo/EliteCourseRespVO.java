package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherBasicInfoRespVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseCategoryDO;
import com.xt.hsk.module.edu.enums.elitecourse.ClassHourNumberStatusEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseListingMethodEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseListingStatusEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseTypeEnum;
import com.xt.hsk.module.edu.enums.elitecourse.LearningValidityPeriodEnum;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 精品课 Resp VO
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
public class EliteCourseRespVO implements VO {


    /**
     * 精品课ID
     */
    private Long id;


    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;


    /**
     * 一级分类id
     */
    @Trans(type = TransType.SIMPLE,
            target = EliteCourseCategoryDO.class,
            fields = {"nameCn", "nameEn", "nameOt"},
            refs = {"categoryNameCn", "categoryNameEn", "categoryNameOt"}
    )
    private Long primaryCategoryId;


    /**
     * 二级分类id
     */
    private Long secondaryCategoryId;


    /**
     * 课程名称-中文
     */
    private String courseNameCn;


    /**
     * 课程名称-英文
     */
    private String courseNameEn;


    /**
     * 课程名称-其他
     */
    private String courseNameOt;


    /**
     * 课程封面大图URL
     */
    private String coverUrlLarge;


    /**
     * 课程封面小图URL
     */
    private String coverUrlSmall;


    /**
     * 课时数状态 1：课程大纲课时数 2：自定义课时
     * @see ClassHourNumberStatusEnum
     */
    private Integer classHourNumberStatus;


    /**
     * 自定义课时数
     */
    private Integer customClassHourNumber;


    /**
     * 划线价格(人民币)
     */
    private BigDecimal originalPriceCn;


    /**
     * 售卖价格(人民币)
     */
    private BigDecimal sellingPriceCn;


    /**
     * 划线价格(美元)
     */
    private BigDecimal originalPriceEn;


    /**
     * 售卖价格(美元)
     */
    private BigDecimal sellingPriceEn;


    /**
     * 划线价格(越南盾)
     */
    private BigDecimal originalPriceOt;


    /**
     * 售卖价格(越南盾)
     */
    private BigDecimal sellingPriceOt;


    /**
     * 课程详情内容
     */
    private String courseDetail;


    /**
     * 上架方式 1：立即上架 2：定时上架 3：暂不上架
     * @see EliteCourseListingMethodEnum
     */
    private Integer listingMethod;


    /**
     * 上架状态 1：上架 2：下架 3：待上架
     * @see EliteCourseListingStatusEnum
     */
    private Integer listingStatus;

    /**
     * 上架时间
     */
    private LocalDateTime listingTime;


    /**
     * 销售基数
     */
    private Integer salesBase;


    /**
     * 学习有效期 1：长期有效 2：按截止日期 3：按天数
     * @see LearningValidityPeriodEnum
     */
    private Integer learningValidityPeriod;


    /**
     * 截至日期
     */
    private LocalDateTime deadline;


    /**
     * 有效天数
     */
    private Integer effectiveDays;


    /**
     * 生效模式
     */
    private Integer effectModel;


    /**
     * 排序序号
     */
    private Integer sort;


    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;


    /**
     * 报名人数
     */
    private Integer enrollmentCount;


    /**
     * 类型 1.普通课程 2.公开课
     * @see EliteCourseTypeEnum
     */
    private Integer type;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 分类名称-中文
     */
    private String categoryNameCn;

    /**
     * 分类名称-英文
     */
    private String categoryNameEn;

    /**
     * 分类名称-其他
     */
    private String categoryNameOt;

    /**
     * 学习有效期描述：长期有效、日期 、x天
     *
     * @see LearningValidityPeriodEnum
     */
    private String learningValidityPeriodStr;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最近更新人ID
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "updaterName")
    private String updater;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 讲师id列表
     */
    private List<Long> teacherIdList;

    /**
     * 讲师信息列表
     */
    private List<TeacherBasicInfoRespVO> teacherList;

    /**
     * 在读人数
     */
    private Integer readingCount;

    /**
     * 是否是推荐课程
     */
    private Boolean isRecommended;

}