package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

/**
 * 精品课讲师关联 save req vo
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
public class EliteCourseTeacherSaveReqVO {

/**
* 精品课程讲师关联id
*/
    private Long id;

/**
* 讲师id（edu_teacher表id）
*/
    @NotNull(message = "讲师id（edu_teacher表id）不能为空")
    private Long teacherId;

/**
* 精品课程id（edu_elite_course表id）
*/
    @NotNull(message = "精品课程id（edu_elite_course表id）不能为空")
    private Long eliteCourseId;

}