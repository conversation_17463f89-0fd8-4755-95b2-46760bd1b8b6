package com.xt.hsk.module.edu.controller.admin.userquestionanswerrecord.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class UserQuestionAnswerRecordSaveReqVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 题目ID
     */
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 教材ID
     */
    private Long textbookId;

    /**
     * 章节ID
     */
    private Long chapterId;

    /**
     * 单元ID
     */
    private Long unitId;

    /**
     * 题型ID
     */
    private Long questionTypeId;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 练习模式：1-单独练习 2-全真模考 3-30分钟模考 4-15分钟模考
     */
    @NotNull(message = "练习模式：1-单独练习 2-全真模考 3-30分钟模考 4-15分钟模考不能为空")
    private Integer practiceMode;

    /**
     * 考试/练习ID（关联到具体的考试或练习）
     */
    private Long practiceId;

    /**
     * 作答耗时（秒）
     */
    private Integer answerTime;

    /**
     * 作答日期（便于按日统计）
     */
    private LocalDate answerDate;

    /**
     * 开始作答时间
     */
    private LocalDateTime startTime;

    /**
     * 结束作答时间
     */
    private LocalDateTime endTime;

    /**
     * 记录状态 1 进行中 2 已提交 3 ai批改未完成  4 ai批改完成
     */
    private Boolean recordStatus;
    /**
     * 题目总数量
     */
    private Integer questionNum;
    /**
     * 已正确数量
     */
    private Integer correctNum;
}