package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.xt.hsk.module.edu.enums.elitecourse.EliteClassHourTypeEnum;
import lombok.Data;

/**
 * 精品课程学习详情 resp vo
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
public class EliteCourseStudyDetailRespVO extends BaseCourseStudyRespVO {

    /**
     * 课时id（edu_elite_class_hour表id）
     */
    private Long classHourId;

    /**
     * 课时名称-中文
     */
    private String classHourNameCn;

    /**
     * 课时名称-英文
     */
    private String classHourNameEn;

    /**
     * 课时名称-其他
     */
    private String classHourNameOt;

    /**
     * 课时类型 1：直播课 2：录播课
     *
     * @see EliteClassHourTypeEnum
     */
    private Integer classHourType;

    /**
     * 视频id
     */
    private Long videoId;
}