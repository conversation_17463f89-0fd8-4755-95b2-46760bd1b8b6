package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 精品课程学习明细 分页 req vo
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EliteCourseStudyDetailPageReqVO extends PageParam {

    /**
     * 课程登记id（edu_elite_course_register表id）
     */
    @NotNull(message = "课程登记ID不能为空")
    private Long courseRegisterId;
} 