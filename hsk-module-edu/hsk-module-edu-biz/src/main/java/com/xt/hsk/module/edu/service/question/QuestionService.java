package com.xt.hsk.module.edu.service.question;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionPageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRespVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionTextbookCount;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypeCount;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionUnitCount;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionSearchReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.TextbookChapterQuestionRespVO;
import com.xt.hsk.module.edu.dal.dataobject.question.QuestionDO;
import com.xt.hsk.module.edu.job.question.QuestionJobVo;
import jakarta.validation.Valid;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 题目 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionService extends IService<QuestionDO> {
    PageResult<QuestionDO> selectPage(@Valid QuestionPageReqVO pageReqVO);

    long countByTextbookId(Long id);

    long countByUnitId(Long id);

    List<QuestionTypeCount> countBySubjectAndQuestionTypeIds(Integer subject, Set<Long> questionTypeIds);

    Long getMaxId();

    List<QuestionJobVo> getQuestionJobVos(Long startId, int batchSize);

    List<QuestionUnitCount> countByUnitIds(Set<Long> unitIds);

    PageResult<QuestionRespVO> queryQuestionPage(@Param("req") QuestionPageReqVO pageReqVO);

    Long queryQuestionCount(@Param("req") QuestionPageReqVO pageReqVO);

    List<QuestionTypeCountRespVO> getQuestionTypeCountRespVOByUnitIds(QuestionSearchReqVO reqVO);

    Long countByChapterId(Long id);

    List<TextbookChapterQuestionRespVO> getTextbookChapterQuestions(QuestionSearchReqVO reqVO);

    List<Long> selectUserPracticeQuestions(QuestionSearchReqVO reqVO);

    QuestionRespVO getQuestionById(Long id);

    List<Long> queryQuestionIdList();

    List<QuestionTextbookCount> countByTextbookIds(Set<Long> textbookIds);

    /**
     * 根据题目id列表查询题目
     *
     * @param idList ID列表
     * @return 题目列表
     */
    List<QuestionRespVO> queryQuestionByIdList(List<Long> idList);

    List<QuestionJobVo> getQuestionJobVos(List<Long> questionIds);

    List<Long> randomQuestionIds(int level, int subject, int batchSize);
}