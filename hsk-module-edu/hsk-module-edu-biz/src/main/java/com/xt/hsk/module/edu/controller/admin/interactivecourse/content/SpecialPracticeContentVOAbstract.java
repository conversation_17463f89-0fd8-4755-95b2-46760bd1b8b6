package com.xt.hsk.module.edu.controller.admin.interactivecourse.content;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 专项练习题目内容
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SpecialPracticeContentVOAbstract extends AbstractQuestionContentBaseVO {

    /**
     * 练习组ID
     */
    @NotNull(message = "练习组ID不能为空")
    private Long practiceGroupId;

    /**
     * 版本号
     */
    @NotNull(message = "版本号不能为空")
    private Integer version;

} 