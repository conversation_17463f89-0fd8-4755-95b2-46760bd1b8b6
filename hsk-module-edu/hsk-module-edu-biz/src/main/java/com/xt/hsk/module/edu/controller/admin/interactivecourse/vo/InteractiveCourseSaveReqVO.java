package com.xt.hsk.module.edu.controller.admin.interactivecourse.vo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 互动课程创建参数类
 *
 * <AUTHOR>
 * @since 2025/05/23
 */
@Data
public class InteractiveCourseSaveReqVO {

    /**
     * 编号
     */
    private Long id;

    /**
     * 课程类型 1-普通课程
     */
    @NotNull(message = "课程类型不能为空")
    private Integer type;

    /**
     * 课程名称-中文
     */
    @NotEmpty(message = "课程名称不能为空")
    private String courseNameCn;

    /**
     * 课程名称-英文
     */
    private String courseNameEn;

    /**
     * 课程名称 OT
     */
    private String courseNameOt;

    /**
     * 展示状态 0-隐藏 1-显示
     */
    @NotNull(message = "请选择展示状态")
    private Integer displayStatus;


    /**
     * HSK等级
     */
    @NotNull(message = "请选择HSK等级")
    private Integer hskLevel;

    /**
     * 课程封面 url
     */
    @NotEmpty(message = "课程封面不能为空")
    private String coverUrl;

    /**
     * 学习基数
     */
    private Integer learningBase;

}