package com.xt.hsk.module.edu.controller.admin.interactivecourse.content;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 生词信息
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
public class VocabularyInfoVO {

    /**
     * 字词ID
     */
    @NotNull(message = "字词ID不能为空")
    private Long wordId;

    /**
     * 序号
     */
    @NotNull(message = "生词序号不能为空")
    private Integer sort;

} 