package com.xt.hsk.module.edu.controller.admin.textbook;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.ChapterPageReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.ChapterRespVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.ChapterSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.TextbookPageReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.TextbookRespVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.TextbookSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.UnitTreeReqVO;
import com.xt.hsk.module.edu.controller.admin.textbook.vo.UnitTreeResultVO;
import com.xt.hsk.module.edu.service.textbook.TextbookManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 教材管理")
@RestController
@RequestMapping("/edu/textbook")
@Validated
public class TextbookController {

    @Resource
    private TextbookManager textbookManager;

    @PostMapping("/create")
    @Operation(summary = "创建教材")
    @PreAuthorize("@ss.hasPermission('edu:textbook:create')")
    @LogRecord(type = LogRecordType.TEXTBOOK_TYPE, subType = "创建教材", bizNo = "{{#textbookId}}", success = "创建教材：【{{#textbook.nameCn}}】，HSK等级：{{#textbook.hskLevel}}")
    public CommonResult<Long> createTextbook(@Valid @RequestBody TextbookSaveReqVO createReqVO) {
        return success(textbookManager.createTextbook(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新教材")
    @PreAuthorize("@ss.hasPermission('edu:textbook:update')")
    @LogRecord(type = LogRecordType.TEXTBOOK_TYPE, subType = "修改教材", bizNo = "{{#updateReqVO.id}}", success = "修改教材：【{{#textbook.nameCn}}】，HSK等级：{{#textbook.hskLevel}}")
    public CommonResult<Boolean> updateTextbook(@Valid @RequestBody TextbookSaveReqVO updateReqVO) {
        textbookManager.updateTextbook(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除教材")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:textbook:delete')")
    @LogRecord(type = LogRecordType.TEXTBOOK_TYPE, subType = "删除教材", bizNo = "{{#id}}", success = "删除教材：【{{#textbook.nameCn}}】，HSK等级：{{#textbook.hskLevel}}")
    public CommonResult<Boolean> deleteTextbook(@RequestParam("id") Long id) {
        textbookManager.deleteTextbook(id);
        return success(true);
    }

    @PostMapping("/get")
    @Operation(summary = "获得教材")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:textbook:query')")
    public CommonResult<TextbookRespVO> getTextbook(@RequestParam("id") Long id) {
        TextbookRespVO textbook = textbookManager.getTextbook(id);
        return success(BeanUtils.toBean(textbook, TextbookRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得教材分页")
    @PreAuthorize("@ss.hasPermission('edu:textbook:query')")
    public CommonResult<PageResult<TextbookRespVO>> getTextbookPage(@Valid
                                                                    @RequestBody TextbookPageReqVO pageReqVO) {
        return success(textbookManager.getTextbookPage(pageReqVO));
    }


    @PostMapping("/pageChapter")
    @Operation(summary = "获得课程大纲分页")
    @PreAuthorize("@ss.hasPermission('edu:chapter:query')")
    public CommonResult<PageResult<ChapterRespVO>> getChapterPage(@Valid
                                                    @RequestBody ChapterPageReqVO pageReqVO) {
        PageResult<ChapterRespVO> pageResult = textbookManager.getChapterPage(pageReqVO);
        return success(pageResult);
    }

    @Operation(summary = "获取大纲详情")
    @Parameter(name = "id", description = "ID", required = true)
    @PostMapping("/getChapter")
    public CommonResult<ChapterRespVO> getChapter(@RequestParam("id") Long id) {
        ChapterRespVO chapter = textbookManager.getChapter(id);
        return success(chapter);
    }

    @Operation(summary = "保存大纲详情")
    @PostMapping("/saveChapter")
    @LogRecord(type = LogRecordType.CHAPTER_TYPE, subType = "保存章节", bizNo = "{{#chapterId}}", success = "保存章节：【{{#chapter.chapterNameCn}}】，教材：{{#textbookName}}")
    public CommonResult<Long> saveChapter(@Valid @RequestBody ChapterSaveReqVO chapterSaveReqVO) {
        return success(textbookManager.createChapter(chapterSaveReqVO));
    }

    @Operation(summary = "删除章节")
    @Parameter(name = "id", description = "章节ID", required = true)
    @PostMapping("/deleteChapter")
    @LogRecord(type = LogRecordType.CHAPTER_TYPE, subType = "删除章节", bizNo = "{{#id}}", success = "删除章节：【{{#chapter.chapterNameCn}}】，教材：{{#textbookName}}")
    public CommonResult<Boolean> deleteChapter(@Valid @RequestParam("id") Long id) {
        textbookManager.deleteChapter(id);
        return success(true);
    }

    @Operation(summary = "获取章节下是否有题目")
    @Parameter(name = "id", description = "章节ID", required = true)
    @PostMapping("/getChapterHasQuestion")
    public CommonResult<Boolean> getChapterHasQuestion(@RequestParam("id") Long id) {
        return success(textbookManager.countByChapterId(id) > 0);
    }

    /**
     * 禁用/启用大纲
     *
     * @param id     大纲id
     * @param status 状态
     */
    @Operation(summary = "禁用/启用大纲")
    @PostMapping("/")
    @Parameter(name = "id", description = "id", required = true)
    @Parameter(name = "status", description = "状态", required = true)
    @LogRecord(type = LogRecordType.CHAPTER_TYPE, subType = "更新章节状态", bizNo = "{{#id}}", success = "{{#statusText}}章节：【{{#chapter.chapterNameCn}}】，教材：{{#textbookName}}")
    public CommonResult<Boolean> enableOrDisableChapter(@RequestParam("id") Long id, @RequestParam("status") Integer status) {
        textbookManager.enableOrDisableChapter(id, status);
        return success(true);
    }

    /**
     * 删除单元
     *
     * @param id 单元id
     */
    @Operation(summary = "删除单元")
    @PostMapping("/deleteUnit")
    @LogRecord(type = LogRecordType.CHAPTER_TYPE, subType = "删除单元", bizNo = "{{#id}}", success = "删除单元：【{{#unit.unitNameCn}}】，章节：{{#chapterName}}")
    public CommonResult<Boolean> deleteUnit(@RequestParam("id") Long id) {
        return success(textbookManager.deleteUnitById(id));
    }

    /**
     * 获取单元下是否有题目
     *
     * @param id 单元id
     */
    @Operation(summary = "获取单元下是否有题目")
    @Parameter(name = "id", description = "单元ID", required = true)
    @PostMapping("/getUnitHasQuestion")
    public CommonResult<Boolean> getUnitHasQuestion(@RequestParam("id") Long id) {
        return success(textbookManager.countByUnitId(id) > 0);
    }

    @Operation(summary = "教材/科目/章节/单元")
    @PostMapping("/treeList")
    public CommonResult<List<UnitTreeResultVO>> tree(@RequestBody UnitTreeReqVO treeReqVO) {

        return success(textbookManager.getUnitTree(treeReqVO));
    }

    @PostMapping("/mockTextbook")
    public CommonResult<Long> mockTextbook() {
        return success(textbookManager.mockTextbook());
    }
}