package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseLearningStatusEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseRegisterTypeEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 精品课用户 Resp VO
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
public class EliteCourseUserRespVO {


    /**
     * 课程登记记录id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 课程类型：1.普通课程 2.公开课
     *
     * @see EliteCourseTypeEnum
     */
    private Integer courseType;

    /**
     * 预约或购买时间
     */
    private LocalDateTime enrollmentTime;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 手机区号
     */
    private String countryCode;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 学习状态：1.正常 2.已过期
     *
     * @see EliteCourseLearningStatusEnum
     */
    private Integer learningStatus;

    /**
     * 学习状态描述：正常、已过期
     *
     * @see EliteCourseLearningStatusEnum
     */
    private String learningStatusStr;

    /**
     * 报名途径：1.线上购买 2.线下报名(后台)
     *
     * @see EliteCourseRegisterTypeEnum
     */
    private Integer registerType;

    /**
     * 报名途径描述：线上购买、线下报名(后台)
     *
     * @see EliteCourseRegisterTypeEnum
     */
    private String registerTypeStr;
}