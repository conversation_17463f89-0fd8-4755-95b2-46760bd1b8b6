package com.xt.hsk.module.edu.controller.admin.textbook.vo;

import lombok.*;

import jakarta.validation.constraints.*;

@Data
public class UnitSaveReqVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 所属章节
     */
    @NotNull(message = "所属章节不能为空")
    private Long chapterId;

    /**
     * 教材id
     */
    @NotNull(message = "教材id不能为空")
    private Long textbookId;


    /**
     * 单元名称
     */
    @NotEmpty(message = "单元名称不能为空")
    private String unitNameCn;

    /**
     * 单元名称
     */
    private String unitNameEn;

    /**
     * 单元名称
     */
    private String unitNameOt;

    /**
     * 排序序号
     */
    @NotNull(message = "排序序号不能为空")
    private Integer sort;
    /**
     * 科目（1=听力，2=阅读，4=书写）
     */

    private Integer subject;
    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;

//    /**
//     * 难度等级 1 简单 2中等 3 困难
//     */
//    private Integer difficultyLevel;

}