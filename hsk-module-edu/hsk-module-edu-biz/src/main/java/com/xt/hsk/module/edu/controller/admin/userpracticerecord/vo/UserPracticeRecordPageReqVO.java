package com.xt.hsk.module.edu.controller.admin.userpracticerecord.vo;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import com.xt.hsk.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserPracticeRecordPageReqVO extends PageParam {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 教材ID
     */
    private Long textbookId;

    /**
     * 章节ID
     */
    private Long chapterId;

    /**
     * 科目
     */
    private Integer subject;

    /**
     * 单元序号
     */
    private Integer unitSort;

    /**
     * 题型ID
     */
    private Long questionTypeId;

    /**
     * 题目总数量
     */
    private Integer questionNum;

    /**
     * 已作答数量
     */
    private Integer answerNum;

    /**
     * 已正确数量
     */
    private Integer correctNum;

    /**
     * 作答总耗时（秒）
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Integer[] answerTime;

    /**
     * 开始作答时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] startTime;

    /**
     * 结束作答时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] endTime;

    /**
     * 记录状态 1 进行中 2 生成报告
     */
    private Integer recordStatus;

    /**
     * 本练习记录是否为最新数据 0 否 1 是
     */
    private Boolean isNewest;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
    /**
     * 本次练习的全部题目id(英文逗号拼接)
     */
    private String questionIds;

    /**
     * 互动课单元ID（当练习来源于互动课时）
     */
    private Long interactiveCourseUnitId;
}