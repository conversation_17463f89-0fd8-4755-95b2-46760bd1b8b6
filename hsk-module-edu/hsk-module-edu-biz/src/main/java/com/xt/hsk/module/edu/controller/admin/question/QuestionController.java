package com.xt.hsk.module.edu.controller.admin.question;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.IMPORT_TEMPLATE_SELECT_ERROR;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.IMPORT_TEMPLATE_TYPE_ERROR;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static com.xt.hsk.framework.common.pojo.CommonResult.error;
import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import cn.dev33.satoken.annotation.SaIgnore;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.context.LogRecordContext;
import com.xt.hsk.framework.common.exception.ServerException;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.ImportResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionPageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRespVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.QuestionDO;
import com.xt.hsk.module.edu.service.question.QuestionManager;
import com.xt.hsk.module.infra.api.export.ExportTaskApi;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import com.xt.hsk.module.thirdparty.api.XunFeiApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "管理后台 - 题目")
@RestController
@RequestMapping("/edu/question")
@Validated
@Slf4j
public class QuestionController {

    @Resource
    private QuestionManager questionManager;
    @Resource
    private XunFeiApi xunFeiApi;

    @Resource
    private ExportTaskApi exportTaskApi;

    @PostMapping("/create")
    @Operation(summary = "创建题目")
    @PreAuthorize("@ss.hasPermission('edu:question:create')")
    @TransMethodResult
    public CommonResult<Long> createQuestion(@Valid @RequestBody QuestionSaveReqVO createReqVO) {
        return success(questionManager.createQuestion(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新题目")
    @PreAuthorize("@ss.hasPermission('edu:question:update')")
    public CommonResult<Boolean> updateQuestion(@Valid @RequestBody QuestionSaveReqVO updateReqVO) {
        questionManager.updateQuestion(updateReqVO);
        return success(true);
    }

    @PostMapping("/updateStatus")
    @Operation(summary = "修改题目状态")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:question:updateStatus')")
    public CommonResult<Boolean> updateStatus(@Valid @RequestBody QuestionCommonStatusVO updateReqVO) {
        questionManager.updateQuestionCommonStatus(updateReqVO);
        return success(true);
    }




    @PostMapping("/get")
    @Operation(summary = "获得题目")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:question:query')")
    @TransMethodResult
    public CommonResult<QuestionRespVO> getQuestion(@RequestParam("id") Long id) {
        QuestionDO question = questionManager.getQuestion(id);
        return success(BeanUtils.toBean(question, QuestionRespVO.class));
    }

    @PostMapping("/getByQuestionId")
    @Operation(summary = "获得题目详情")
    @PreAuthorize("@ss.hasPermission('edu:question:getByQuestionId')")
    @TransMethodResult
    public CommonResult<QuestionRespVO> getByQuestionId(@RequestParam("questionId") Long questionId) {
        QuestionRespVO questionDetail = questionManager.getQuestionDetail(questionId);
        return success(questionDetail);
    }


    @PostMapping("/page")
    @Operation(summary = "获得题目分页")
    @PreAuthorize("@ss.hasPermission('edu:question:query')")
    @TransMethodResult
    public CommonResult<PageResult<QuestionRespVO>> getQuestionPage(@Valid @RequestBody QuestionPageReqVO pageReqVO) {
        PageResult<QuestionRespVO> pageResult = questionManager.getQuestionPage(pageReqVO);
        return success(pageResult);
    }


    /**
     * 下载导入模板
     *
     * @param response 响应
     */
    @PostMapping("/import/template")
    public void downloadImportTemplate(HttpServletResponse response) {
        questionManager.downloadImportTemplate(response);
    }

    @PostMapping("/executeData")
    public void executeData() {
        questionManager.addUrl();
    }

    /**
     * 导入题目
     *
     * @param file 文件
     * @return
     */
    @PostMapping("/import")
    @TransMethodResult
    public CommonResult<ImportResult> importQuestion(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return CommonResult.error(IMPORT_TEMPLATE_SELECT_ERROR);
        }
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || (!originalFilename.endsWith(".xlsx") && !originalFilename.endsWith(".xls"))) {
            return CommonResult.error(IMPORT_TEMPLATE_TYPE_ERROR);
        }
        ImportResult result = questionManager.importQuestion(file);
        return success(result);
    }

    @PostMapping("/export")
    @Operation(summary = "导出汉语词典基础数据")
    @PreAuthorize("@ss.hasPermission('edu:question:export')")
    public CommonResult<Long> export(@Valid @RequestBody QuestionPageReqVO pageReqVO) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 将导出请求参数和任务名称一起传递
            ExportTaskParams taskParams = new ExportTaskParams();
            taskParams.setTaskName(pageReqVO.getTaskName());
            taskParams.setQueryParams(pageReqVO);

            String params = objectMapper.writeValueAsString(taskParams);
            Long taskId = exportTaskApi.createExportTask(pageReqVO.getTaskName(),
                    ExportTaskTypeEnum.EDU_QUESTION, params);
            LogRecordContext.putVariable("taskId", taskId);
            return success(taskId);
        } catch (Exception e) {
            log.error("创建汉语词典基础数据导出任务失败", e);
            return CommonResult.error(500, "创建汉语词典基础数据导出任务失败：" + e.getMessage());
        }
    }

    @PermitAll
    @PostMapping("/testXunFeiUpload")
    public CommonResult<String> testXunFeiUpload() {

        String result = xunFeiApi.upload("https://hsk-dev.obs.ap-southeast-3.myhuaweicloud.com/20250703/output_1751535052472.mp3", "/edu/question/testXunFeiCallback");
        return success(result);
    }

    /**
     * 测试讯飞回调
     * @param orderId 订单id
     * @param status 状态
     * @return
     */
    @PermitAll
    @GetMapping("/testXunFeiCallback")
    public CommonResult<String> testXunFeiCallback(String orderId, String status) {
        log.info("=== 回调内容: orderId{},status{}", orderId, status);
        return success("success");
    }

    /**
     * 测试讯飞获取结果
     * @param orderId 订单id
     * @param status 状态
     * @return
     */
    @PermitAll
    @PostMapping("/testXunFeiGetResult")
    public CommonResult<String> testXunFeiGetResult(String orderId, String status) {
        String result = xunFeiApi.getResult(orderId);
        return success(result);
    }
}