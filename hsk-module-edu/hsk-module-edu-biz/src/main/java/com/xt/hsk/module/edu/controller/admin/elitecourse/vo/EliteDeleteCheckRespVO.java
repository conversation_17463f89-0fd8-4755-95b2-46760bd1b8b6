package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 删除检查 响应 VO
 * <p>
 * 通用删除前检查对象，适用于课程分类、课程等
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EliteDeleteCheckRespVO {

    /**
     * 是否可以删除
     */
    private Boolean canDelete;

    /**
     * 关联数据数量
     */
    private Long relatedCount;
} 