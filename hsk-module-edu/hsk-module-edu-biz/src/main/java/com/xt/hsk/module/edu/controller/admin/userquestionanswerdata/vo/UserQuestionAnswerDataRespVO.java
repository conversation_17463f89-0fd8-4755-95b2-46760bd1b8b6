package com.xt.hsk.module.edu.controller.admin.userquestionanswerdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import cn.idev.excel.annotation.*;

@Data
public class UserQuestionAnswerDataRespVO {


    /**
     * 主键
     */
    private Long id;


    /**
     * 作答id
     */
    private Long recordId;


    /**
     * 用户答案
     */
    private String userAnswer;


    /**
     * 参考答案
     */
    private String answer;


    /**
     * 是否正确 0-错误 1-正确
     */
    private Boolean isCorrect;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 题目id
     */
    private Long questionId;
    /**
     * 题目明细id
     */
    private Long questionDetailId;
    /**
     * 版本
     */
    private Integer version;


}