package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import cn.idev.excel.annotation.*;

/**
 * 精品课讲师关联 Resp Vo
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
public class EliteCourseTeacherRespVO {


/**
* 精品课程讲师关联id
*/
    private Long id;


/**
* 讲师id（edu_teacher表id）
*/
    private Long teacherId;


/**
* 精品课程id（edu_elite_course表id）
*/
    private Long eliteCourseId;



/**
* 创建时间
*/
    private LocalDateTime createTime;




}