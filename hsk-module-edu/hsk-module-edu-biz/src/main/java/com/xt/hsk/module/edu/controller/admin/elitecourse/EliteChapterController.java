package com.xt.hsk.module.edu.controller.admin.elitecourse;

import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteChapterPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteChapterRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteChapterSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteChapterDO;
import com.xt.hsk.module.edu.manager.elitecourse.EliteChapterManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

/**
 * 精品课-章节 控制器
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@RestController
@Validated
@RequestMapping("/edu/elite-chapter")
public class EliteChapterController {

    @Resource
    private EliteChapterManager eliteChapterManager;

    /**
     * 创建精品课章节
     */
    @PostMapping("/create")
    @LogRecord(type = LogRecordType.ELITE_COURSE_CHAPTER_TYPE, bizNo = "{{#createReqVO.id}}", success = "创建精品课章节")
    @PreAuthorize("@ss.hasPermission('edu:elite-chapter:create')")
    public CommonResult<Boolean> createEliteChapter(@Valid @RequestBody EliteChapterSaveReqVO createReqVO) {
        eliteChapterManager.createEliteChapter(createReqVO);
        return success(true);
    }

    /**
     * 更新精品课章节
     */
    @PutMapping("/update")
    @LogRecord(type = LogRecordType.ELITE_COURSE_CHAPTER_TYPE, bizNo = "{{#updateReqVO.id}}", success = "更新精品课章节")
    @PreAuthorize("@ss.hasPermission('edu:elite-chapter:update')")
    public CommonResult<Boolean> updateEliteChapter(@RequestBody EliteChapterSaveReqVO updateReqVO) {
        eliteChapterManager.updateEliteChapter(updateReqVO);
        return success(true);
    }

    /**
     * 删除精品课章节
     */
    @DeleteMapping("/delete")
    @LogRecord(type = LogRecordType.ELITE_COURSE_CHAPTER_TYPE, bizNo = "{{#id}}", success = "删除精品课章节")
    @PreAuthorize("@ss.hasPermission('edu:elite-chapter:delete')")
    public CommonResult<Boolean> deleteEliteChapter(@RequestParam("id") Long id) {
        eliteChapterManager.deleteEliteChapter(id);
        return success(true);
    }

    /**
     * 根据id获取精品课章节
     */
    @PostMapping("/get")
    @PreAuthorize("@ss.hasPermission('edu:elite-chapter:query')")
    public CommonResult<EliteChapterRespVO> getEliteChapter(@RequestParam("id") Long id) {
        EliteChapterDO eliteChapter = eliteChapterManager.getEliteChapter(id);
        return success(BeanUtils.toBean(eliteChapter, EliteChapterRespVO.class));
    }

    /**
     * 分页获取精品课章节
     */
    @PostMapping("/page")
    @PreAuthorize("@ss.hasPermission('edu:elite-chapter:query')")
    public CommonResult<PageResult<EliteChapterRespVO>> getEliteChapterPage(@Valid EliteChapterPageReqVO pageReqVO) {
        PageResult<EliteChapterDO> pageResult = eliteChapterManager.getEliteChapterPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EliteChapterRespVO.class));
    }

    /**
     * 修改精品课章节排序
     */
    @PutMapping("/sort")
    @LogRecord(type = LogRecordType.ELITE_COURSE_CHAPTER_TYPE, bizNo = "{{#id}}", success = "修改精品课章节排序")
    @PreAuthorize("@ss.hasPermission('edu:elite-chapter:update')")
    public CommonResult<Boolean> updateEliteChapterSort(@RequestParam("id") Long id, @RequestParam("sort") Integer sort) {
        eliteChapterManager.updateEliteChapterSort(id, sort);
        return success(true);
    }

    /**
     * 根据精品课id获取章节列表
     */
    @GetMapping("/list-by-course")
    @PreAuthorize("@ss.hasPermission('edu:elite-chapter:query')")
    public CommonResult<List<EliteChapterRespVO>> listByCourseId(@RequestParam("courseId") Long courseId) {
        return success(eliteChapterManager.listByCourseId(courseId));
    }

}