package com.xt.hsk.module.edu.controller.admin.elitecourse;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourReuseRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteClassHourDO;
import com.xt.hsk.module.edu.manager.elitecourse.EliteClassHourManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 精品课-课时 控制器
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@RestController
@Validated
@RequestMapping("/edu/elite-class-hour")
public class EliteClassHourController {

    @Resource
    private EliteClassHourManager eliteClassHourManager;

    /**
     * 创建精品课课时
     */
    @PostMapping("/create")
    @LogRecord(type = LogRecordType.ELITE_COURSE_CLASS_HOUR_TYPE,
        subType = "创建精品课课时",
        bizNo = "{{#classHourId}}",
        success = "创建精品课课时：【{{#classHour.classHourNameCn}}】,课程【{{#courseName}}】")
    @PreAuthorize("@ss.hasPermission('edu:elite-class-hour:create')")
    public CommonResult<Boolean> createEliteClassHour(@Valid @RequestBody EliteClassHourSaveReqVO createReqVO) {
        eliteClassHourManager.createEliteClassHour(createReqVO);
        return success(true);
    }

    /**
     * 更新精品课课时
     */
    @PutMapping("/update")
    @LogRecord(type = LogRecordType.ELITE_COURSE_CLASS_HOUR_TYPE,
        subType = "修改精品课课时", bizNo = "{{#updateReqVO.id}}",
        success = "修改精品课课时：【{{#classHourNameCn}}】,课程【{{#courseName}}】")
    @PreAuthorize("@ss.hasPermission('edu:elite-class-hour:update')")
    public CommonResult<Boolean> updateEliteClassHour(@Valid @RequestBody EliteClassHourSaveReqVO updateReqVO) {
        eliteClassHourManager.updateEliteClassHour(updateReqVO);
        return success(true);
    }

    /**
     * 删除精品课课时
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('edu:elite-class-hour:delete')")
    public CommonResult<Boolean> deleteEliteClassHour(@RequestBody EliteClassHourSaveReqVO reqVO) {
        eliteClassHourManager.deleteEliteClassHour(reqVO);
        return success(true);
    }

    /**
     * 根据id获取精品课课时
     */
    @PostMapping("/get")
    @PreAuthorize("@ss.hasPermission('edu:elite-class-hour:query')")
    public CommonResult<EliteClassHourRespVO> getEliteClassHour(@RequestParam("id") Long id) {
        EliteClassHourDO eliteClassHour = eliteClassHourManager.getEliteClassHour(id);
        return success(BeanUtils.toBean(eliteClassHour, EliteClassHourRespVO.class));
    }

    /**
     * 分页获取精品课课时
     */
    @PostMapping("/page")
    @PreAuthorize("@ss.hasPermission('edu:elite-class-hour:query')")
    public CommonResult<PageResult<EliteClassHourRespVO>> getEliteClassHourPage(@Valid EliteClassHourPageReqVO pageReqVO) {
        PageResult<EliteClassHourDO> pageResult = eliteClassHourManager.getEliteClassHourPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EliteClassHourRespVO.class));
    }

    /**
     * 修改精品课课时排序
     */
    @PutMapping("/sort")
    @LogRecord(type = LogRecordType.ELITE_COURSE_CLASS_HOUR_TYPE,
        subType = "修改精品课课时排序",
        bizNo = "{{#id}}",
        success = "修改精品课课时排序：【{{#classHour.classHourNameCn}}】从第{{#oldSort}}位调整到第{{#sort}}位")
    @PreAuthorize("@ss.hasPermission('edu:elite-class-hour:update')")
    public CommonResult<Boolean> updateEliteClassHourSort(@RequestParam("id") Long id, @RequestParam("sort") Integer sort) {
        eliteClassHourManager.updateEliteClassHourSort(id, sort);
        return success(true);
    }

    /**
     * 复用精品课课时
     */
    @PostMapping("/reuse")
    @LogRecord(type = LogRecordType.ELITE_COURSE_CLASS_HOUR_TYPE, bizNo = "{{#createReqVO.courseId}}", success = "复用精品课课时")
    @PreAuthorize("@ss.hasPermission('edu:elite-class-hour:create')")
    public CommonResult<Boolean> reuse(@RequestBody EliteClassHourSaveReqVO createReqVO) {
        eliteClassHourManager.copyClassHoursToTargetCourse(createReqVO);
        return success(true);
    }

    /**
     * 获取复用课时
     */
    @GetMapping("/reuse")
    @PreAuthorize("@ss.hasPermission('edu:elite-class-hour:query')")
    public CommonResult<EliteClassHourReuseRespVO> getReuse(@RequestParam("classHourId") Long classHourId) {
        return success(eliteClassHourManager.getReuse(classHourId));
    }
}