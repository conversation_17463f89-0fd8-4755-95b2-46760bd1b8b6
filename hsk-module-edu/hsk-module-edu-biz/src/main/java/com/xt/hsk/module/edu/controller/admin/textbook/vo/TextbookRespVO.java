package com.xt.hsk.module.edu.controller.admin.textbook.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class TextbookRespVO {


    /**
     * 主键
     */
    private Long id;


    /**
     * 教程名 中文
     */
    private String nameCn;


    /**
     * 教程名 英文
     */
    private String nameEn;


    /**
     * 教程名 其他语种
     */
    private String nameOt;


    /**
     * 排序序号
     */
    private Integer sort;


    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;


    /**
     * 教材分类
     */
    private Integer type;


    /**
     * 真题数量
     */
    private Integer questionNumber;


    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 章节信息
     */
    private List<ChapterRespVO> chapterList;


}