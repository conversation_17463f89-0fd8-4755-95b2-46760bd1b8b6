package com.xt.hsk.module.edu.controller.admin.textbook.vo;

import lombok.*;

import com.xt.hsk.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChapterPageReqVO extends PageParam {

    /**
     * 教材id
     */
    private Long textbookId;

    /**
     * 科目（1=听力，2=阅读，4=书写）
     */
    private Integer subject;

    /**
     * 章节名称
     */
    private String chapterNameCn;

    /**
     * 章节名称 英文
     */
    private String chapterNameEn;

    /**
     * 章节名称 其他
     */
    private String chapterNameOt;

    /**
     * 章节序号
     */
    private Integer chapterOrder;
    /**
     * 真题数量
     */
    private Integer questionNumber;


    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /**
     * 状态 0开启 1关闭
     */
    private Integer status;

}