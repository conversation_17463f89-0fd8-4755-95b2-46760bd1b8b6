package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 精品课-课程登记 保存 req vo
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
public class EliteCourseRegisterSaveReqVO {

    /**
     * 精品课程登记ID
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 课程id
     */
    @NotNull(message = "课程id不能为空")
    private Long courseId;

    /**
     * 课程类型：1.普通课程 2.公开课
     */
    private Integer courseType;

    /**
     * 预约或购买时间
     */
    private LocalDateTime enrollmentTime;

    /**
     * 报名途径：1.线上购买 2.线下报名(后台)
     */
    private Integer registerType;

    /**
     * 购买课程订单号
     */
    private String orderNumber;

    /**
     * 订单详情ID
     */
    private Long orderDetailId;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 课程状态 1 正常 2 未开通 3 手动开启
     */
    private Integer courseRegisterStatus;

    /**
     * 用户 ID 列表
     */
    @NotEmpty(message = "用ID列表不能为空")
    private List<Long> userIdList;

}