package com.xt.hsk.module.edu.controller.admin.interactivecourse.vo;

import lombok.Data;

/**
 * 互动课程App详细信息 VO
 *
 * <AUTHOR>
 * @since 2025/06/11
 */
@Data
public class InteractiveCourseAppDetailVO {

    /**
     * 封面URL
     */
    private String coverUrl;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 学习人数
     */
    private Integer studyCount;

    /**
     * 推荐学习时长(秒)
     */
    private Integer recommendedDuration;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 课程位置
     */
    private Integer position;
}
