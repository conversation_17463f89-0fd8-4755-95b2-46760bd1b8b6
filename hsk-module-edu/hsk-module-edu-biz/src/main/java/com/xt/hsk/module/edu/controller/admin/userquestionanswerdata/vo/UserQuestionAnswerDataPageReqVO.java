package com.xt.hsk.module.edu.controller.admin.userquestionanswerdata.vo;

import lombok.*;

import java.util.*;

import io.swagger.v3.oas.annotations.media.Schema;
import com.xt.hsk.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserQuestionAnswerDataPageReqVO extends PageParam {

    /**
     * 作答id
     */
    private Long recordId;

    /**
     * 用户答案
     */
    private String userAnswer;

    /**
     * 参考答案
     */
    private String answer;

    /**
     * 是否正确 0-错误 1-正确
     */
    private Boolean isCorrect;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
    /**
     * 题目id
     */
    private Long questionId;
    /**
     * 题目明细id
     */
    private Long questionDetailId;
    /**
     * 版本
     */
    private Integer version;

}