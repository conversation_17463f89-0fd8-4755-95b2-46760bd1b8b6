package com.xt.hsk.module.edu.controller.admin.interactivecourse.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 互动课程页面 req vo
 *
 * <AUTHOR>
 * @since 2025/05/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InteractiveCoursePageReqVO extends PageParam {

    /**
     * 课程类型
     */
    private String type;

    /**
     * 课程中文名称
     */
    private String courseNameCn;

    /**
     * 显示状态 0-不显示 1-显示
     */
    private Integer displayStatus;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * IDS
     */
    private List<Long> ids;
}