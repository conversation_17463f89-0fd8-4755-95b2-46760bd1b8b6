package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 精品课程学习记录 分页 req vo
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EliteCourseStudyStatsPageReqVO extends PageParam {

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 手机区号
     */
    private String countryCode;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * ID列表
     */
    private List<Long> ids;

}