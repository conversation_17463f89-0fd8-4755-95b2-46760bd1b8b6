package com.xt.hsk.module.edu.controller.admin.interactivecourse.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.common.enums.TextbookTypeEnum;
import com.xt.hsk.module.edu.dal.dataobject.question.questiontype.QuestionTypeDO;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;

/**
 * 互动课关联真题展示 VO
 *
 * <AUTHOR>
 * @since 2025/06/30
 */
@Data
public class UnitQuestionInfoVO implements VO {
    /**
     * 真题练习ID
     */
    private Long questionId;

    /**
     * 版本号
     */
    private Integer version;


    /**
     * HSK等级
     */
    @Trans(type = TransType.ENUM, key = "code", target = HskEnum.class, ref = "hskLevelDesc")
    private Integer hskLevel;
    /**
     * hsk等级
     */
    private String hskLevelDesc;

    /**
     * 是否展示 1展示 0不展示
     */
    @Trans(type = TransType.ENUM, key = "code", target = IsShowEnum.class, ref = "isShowDesc")
    private Integer isShow;
    /**
     * 是否展示 1展示 0不展示
     */
    private String isShowDesc;

    /**
     * 题目编码 #100000开始
     */
    private String questionCode;
    /**
     * 教材分类 考场真题，模拟题
     */
    @Trans(type = TransType.ENUM, key = "code", target = TextbookTypeEnum.class, ref = "textbookTypeDesc")
    private Integer textbookType;
    /**
     * 教材分类 考场真题，模拟题
     */
    private String textbookTypeDesc;

    /**
     * 科目
     */
    @Trans(type = TransType.ENUM, key = "code", target = SubjectEnum.class, ref = "subjectDesc")
    private Integer subject;
    /**
     * 科目
     */
    private String subjectDesc;

    /**
     * 教材ID
     */
    private Long textbookId;
    /**
     * 教材名
     */
    private String textbookNameCn;

    /**
     * 排序 关联表保存的排序 不是题目表中的排序
     */
    private Integer sort;

    /**
     * 题型ID
     */
    @Trans(type = TransType.SIMPLE, target = QuestionTypeDO.class, fields = "nameCn", ref = "questionTypeDesc")
    private Long typeId;
    /**
     * 题型名称
     */
    private String questionTypeDesc;

    Map<String, Object> transMap = new HashMap<>();
}
