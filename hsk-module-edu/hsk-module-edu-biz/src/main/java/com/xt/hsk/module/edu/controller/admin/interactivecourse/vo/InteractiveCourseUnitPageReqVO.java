package com.xt.hsk.module.edu.controller.admin.interactivecourse.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 互动课单元分页 req vo
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InteractiveCourseUnitPageReqVO extends PageParam {

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 单元名称-中文
     */
    private String unitNameCn;

    /**
     * 展示状态 0-隐藏 1-显示
     */
    private Boolean displayStatus;

    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;

    /**
     * 题目来源 1-视频 2-专项练习 3-真题练习
     */
    private Integer questionSource;

    /**
     * 选中的ID列表（用于导出）
     */
    private List<Long> ids;

} 