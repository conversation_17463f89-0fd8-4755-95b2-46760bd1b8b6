package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 精品课-分类 保存 req vo
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
public class EliteCourseCategorySaveReqVO {

    /**
     * 精品课分类ID
     */
    private Long id;

    /**
     * 分类名称-中文
     */
    @NotEmpty(message = "分类名称-中文不能为空")
    private String nameCn;

    /**
     * 分类名称-英文
     */
    private String nameEn;

    /**
     * 分类名称-其他
     */
    private String nameOt;

    /**
     * 一等分类id
     */
    private Long parentId;

    /**
     * HSK等级（1-6）
     */
    @NotNull(message = "HSK等级（1-6）不能为空")
    private Integer hskLevel;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 类型 1.普通课程 2.公开课
     */
    @NotNull(message = "类型 1.普通课程 2.公开课不能为空")
    private Integer type;

}