package com.xt.hsk.module.edu.controller.admin.userquestionanswerdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import jakarta.validation.constraints.*;

@Data
public class UserQuestionAnswerDataSaveReqVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 作答id
     */
    @NotNull(message = "作答id不能为空")
    private Long recordId;

    /**
     * 用户答案
     */
    private String userAnswer;

    /**
     * 参考答案
     */
    private String answer;

    /**
     * 是否正确 0-错误 1-正确
     */
    private Boolean isCorrect;
    /**
     * 题目id
     */
    private Long questionId;
    /**
     * 题目明细id
     */
    private Long questionDetailId;
    /**
     * 版本
     */
    private Integer version;

}