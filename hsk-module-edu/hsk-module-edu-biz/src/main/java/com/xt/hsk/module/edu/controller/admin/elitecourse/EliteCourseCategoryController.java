package com.xt.hsk.module.edu.controller.admin.elitecourse;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseCategoryPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseCategoryRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseCategorySaveReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteDeleteCheckRespVO;
import com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseCategoryDO;
import com.xt.hsk.module.edu.manager.elitecourse.EliteCourseCategoryManager;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 精品课-分类 控制器
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@RestController
@Validated
@RequestMapping("/edu/elite-course-category")
public class EliteCourseCategoryController {


    @Resource
    private EliteCourseCategoryManager courseCategoryManager;

    /**
     * 创建精品课分类
     */
    @PostMapping("/create")
    @LogRecord(type = LogRecordType.ELITE_COURSE_CATEGORY_TYPE, subType = "创建精品课分类",
        bizNo = "{{#categoryId}}",
        success = "创建精品课分类：【{{#category.nameCn}}】")
    @PreAuthorize("@ss.hasPermission('edu:elite-course-category:create')")
    public CommonResult<Long> createCourseCategory(@Valid @RequestBody EliteCourseCategorySaveReqVO createReqVO) {
        return success(courseCategoryManager.createCourseCategory(createReqVO));
    }

    /**
     * 更新精品课分类
     */
    @LogRecord(type = LogRecordType.ELITE_COURSE_CATEGORY_TYPE,
        subType = "修改精品课分类",
        bizNo = "{{#updateReqVO.id}}",
        success = "修改精品课分类：【{{#category.nameCn}}】")
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('edu:elite-course-category:update')")
    public CommonResult<Boolean> updateCourseCategory(@Valid @RequestBody EliteCourseCategorySaveReqVO updateReqVO) {
        courseCategoryManager.updateCourseCategory(updateReqVO);
        return success(true);
    }

    /**
     * 删除精品课分类
     */
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @DeleteMapping("/delete")
    @LogRecord(type = LogRecordType.ELITE_COURSE_CATEGORY_TYPE,
        subType = "删除精品课分类",
        bizNo = "{{#id}}",
        success = "删除精品课分类：【{{#category.nameCn}}】")
    @PreAuthorize("@ss.hasPermission('edu:elite-course-category:delete')")
    public CommonResult<Boolean> deleteCourseCategory(@RequestParam("id") Long id) {
        courseCategoryManager.deleteCourseCategory(id);
        return success(true);
    }

    /**
     * 检查精品课分类是否可以删除
     */
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @GetMapping("/check-delete")
    @PreAuthorize("@ss.hasPermission('edu:elite-course-category:query')")
    public CommonResult<EliteDeleteCheckRespVO> checkCourseCategoryCanDelete(@RequestParam("id") Long id) {
        return success(courseCategoryManager.checkCategoryCanDelete(id));
    }

    /**
     * 根据id获取精品课分类
     */
    @GetMapping("/get")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('edu:elite-course-category:query')")
    public CommonResult<EliteCourseCategoryRespVO> getCourseCategory(@RequestParam("id") Long id) {
        EliteCourseCategoryDO courseCategory = courseCategoryManager.getCourseCategory(id);
        return success(BeanUtils.toBean(courseCategory, EliteCourseCategoryRespVO.class));
    }

    /**
     * 分页获取精品课分类
     */
    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('edu:elite-course-category:query')")
    public CommonResult<PageResult<EliteCourseCategoryRespVO>> getCourseCategoryPage(@Valid @RequestBody EliteCourseCategoryPageReqVO pageReqVO) {
        return success(courseCategoryManager.getCourseCategoryPage(pageReqVO));
    }

    /**
     * 修改精品课分类排序
     */
    @PutMapping("/sort")
    @Parameters({
        @Parameter(name = "id", description = "精品课分类ID"),
        @Parameter(name = "sort", description = "排序")
    })
    @LogRecord(type = LogRecordType.ELITE_COURSE_CATEGORY_TYPE,
        subType = "修改精品课分类排序",
        bizNo = "{{#id}}",
        success = "修改精品课分类排序：【{{#category.nameCn}}】从第{{#oldSort}}位调整到第{{#sort}}位")
    @PreAuthorize("@ss.hasPermission('edu:elite-course-category:update')")
    public CommonResult<Boolean> updateCourseCategorySort(@RequestParam("id") Long id, @RequestParam("sort") Integer sort) {
        courseCategoryManager.updateCourseCategorySort(id, sort);
        return success(true);
    }

    /**
     * 获取所有精品课分类
     */
    @GetMapping("/list")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('edu:elite-course-category:query')")
    public CommonResult<List<EliteCourseCategoryRespVO>> getAll() {
        return success(courseCategoryManager.getAll());
    }

    /**
     * 根据HSK等级获取精品课分类列表
     */
    @GetMapping("/list-by-hsk")
    @PreAuthorize("@ss.hasPermission('edu:elite-course-category:query')")
    public CommonResult<List<EliteCourseCategoryRespVO>> getByHskLevel(@RequestParam("hskLevel") Integer hskLevel) {
        return success(courseCategoryManager.getByHskLevel(hskLevel));
    }
}
