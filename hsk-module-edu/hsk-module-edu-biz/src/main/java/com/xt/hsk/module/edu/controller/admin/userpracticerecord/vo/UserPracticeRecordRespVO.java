package com.xt.hsk.module.edu.controller.admin.userpracticerecord.vo;

import java.time.LocalDateTime;
import lombok.Data;

@Data
public class UserPracticeRecordRespVO {


    /**
     * 主键
     */
    private Long id;


    /**
     * 用户ID
     */
    private Long userId;


    /**
     * HSK等级
     */
    private Integer hskLevel;


    /**
     * 教材ID
     */
    private Long textbookId;


    /**
     * 章节ID
     */
    private Long chapterId;


    /**
     * 科目
     */
    private Integer subject;


    /**
     * 单元序号
     */
    private Integer unitSort;


    /**
     * 题型ID
     */
    private Long questionTypeId;


    /**
     * 题目总数量
     */
    private Integer questionNum;


    /**
     * 已作答数量
     */
    private Integer answerNum;


    /**
     * 已正确数量
     */
    private Integer correctNum;


    /**
     * 作答总耗时（秒）
     */
    private Integer answerTime;


    /**
     * 开始作答时间
     */
    private LocalDateTime startTime;


    /**
     * 结束作答时间
     */
    private LocalDateTime endTime;


    /**
     * 记录状态 1 进行中 2 生成报告
     */
    private Integer recordStatus;


    /**
     * 本练习记录是否为最新数据 0 否 1 是
     */
    private Boolean isNewest;

    /**
     * 互动课单元ID（当练习来源于互动课时）
     */
    private Long interactiveCourseUnitId;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 本次练习的全部题目id(英文逗号拼接)
     */
    private String questionIds = "";
    /**
     * 用户已答题目id(英文逗号拼接)
     */
    private String answerQuestionIds = "";
    /**
     * 用户答了一半的题目id(英文逗号拼接)
     */
    private String halfAnswerQuestionIds = "";
}