package com.xt.hsk.module.edu.controller.admin.textbook.vo;

import lombok.*;

import com.xt.hsk.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UnitPageReqVO extends PageParam {

    /**
     * 所属章节
     */
    private Long chapterId;

    /**
     * 教材id
     */
    private Long textbookId;


    /**
     * 单元名称
     */
    private String unitNameCn;

    /**
     * 单元名称
     */
    private String unitNameEn;

    /**
     * 单元名称
     */
    private String unitNameOt;

    /**
     * 排序序号
     */
    private Integer sort;

//    /**
//     * 难度等级 1 简单 2中等 3 困难
//     */
//    private Integer difficultyLevel;
    /**
     * 科目（1=听力，2=阅读，4=书写）
     */

    private Integer subject;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}