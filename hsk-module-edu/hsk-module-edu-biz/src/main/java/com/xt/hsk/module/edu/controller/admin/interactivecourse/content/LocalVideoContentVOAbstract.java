package com.xt.hsk.module.edu.controller.admin.interactivecourse.content;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 本地视频题目内容
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class LocalVideoContentVOAbstract extends AbstractQuestionContentBaseVO {

    /**
     * 视频名称-中文
     */
    @NotEmpty(message = "视频名称不能为空")
    private String videoNameCn;

    /**
     * 视频名称-英文
     */
    private String videoNameEn;

    /**
     * 视频名称-其他
     */
    private String videoNameOt;

    /**
     * 配套资料类型
     * 1-课件 2-生词
     */
    @NotNull(message = "配套资料类型不能为空")
    private Integer materialType;

    /**
     * 视频尺寸比例 1-9:16 2-16:9
     */
    @NotNull(message = "视频尺寸比例不能为空")
    private Integer aspectRatio;

    /**
     * 视频链接列表
     */
    private List<CourseVideoLinkVO> videoLinkList;

    /**
     * 课件信息列表（当materialType=1时可选填）
     */
    @Valid
    private List<CoursewareInfoVO> coursewareList;

    /**
     * 生词信息列表（当materialType=2时可选填）
     */
    @Valid
    private List<VocabularyInfoVO> vocabularyList;

    /**
     * 自定义校验方法
     */
    public void validate() {
        if (materialType == null) {
            return;
        }

        if (aspectRatio == null) {
            throw new IllegalArgumentException("视频尺寸比例不能为空");
        }

        if (materialType == 1) {
            // 课件类型：支持9:16或16:9
            if (aspectRatio != 1 && aspectRatio != 2) {
                throw new IllegalArgumentException("课件类型的视频尺寸比例只能是1(9:16)或2(16:9)");
            }

            // 如果有生词信息，则不符合要求
            if (vocabularyList != null && !vocabularyList.isEmpty()) {
                throw new IllegalArgumentException("课件类型时，不能包含生词信息");
            }

            // 注意：课件列表可以为空
        } else if (materialType == 2) {
            // 生词类型：只支持16:9
            if (aspectRatio != 2) {
                throw new IllegalArgumentException("生词类型的视频尺寸比例只能是2(16:9)");
            }

            // 如果有课件信息，则不符合要求
            if (coursewareList != null && !coursewareList.isEmpty()) {
                throw new IllegalArgumentException("生词类型时，不能包含课件信息");
            }

            // 注意：生词列表可以为空
        } else {
            throw new IllegalArgumentException("配套资料类型只能是1(课件)或2(生词)");
        }
    }

} 