package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 精品课-目录 Resp VO
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
public class EliteCourseDirectoryRespVO {


    /**
     * 章节ID
     */
    private Long chapterId;

    /**
     * 章节名称-中文
     */
    private String chapterNameCn;

    /**
     * 章节名称-英文
     */
    private String chapterNameEn;

    /**
     * 章节名称-其他
     */
    private String chapterNameOt;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 课时列表
     */
    private List<EliteClassHourRespVO> classHourList;

}