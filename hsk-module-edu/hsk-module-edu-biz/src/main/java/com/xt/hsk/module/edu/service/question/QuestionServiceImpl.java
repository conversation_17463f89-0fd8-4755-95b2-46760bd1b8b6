package com.xt.hsk.module.edu.service.question;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionPageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRespVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionTextbookCount;
import com.xt.hsk.module.edu.api.question.QuestionAnswerRecordApi;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypeCount;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionUnitCount;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionSearchReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.TextbookChapterQuestionRespVO;
import com.xt.hsk.module.edu.job.question.QuestionJobVo;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.xt.hsk.module.edu.dal.dataobject.question.QuestionDO;

import com.xt.hsk.module.edu.dal.mysql.question.QuestionMapper;

import java.util.*;


/**
 * 题目 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class QuestionServiceImpl extends ServiceImpl<QuestionMapper, QuestionDO> implements QuestionService {

    @Resource
    private QuestionMapper questionMapper;

    @Override
    public PageResult<QuestionDO> selectPage(QuestionPageReqVO pageReqVO) {

        return questionMapper.selectPage(pageReqVO);
    }

    @Override
    public long countByTextbookId(Long id) {
        return questionMapper.countByTextbookId(id);
    }

    @Override
    public long countByUnitId(Long id) {
        return questionMapper.countByUnitId(id);
    }


    @Override
    public List<QuestionTypeCount> countBySubjectAndQuestionTypeIds(Integer subject, Set<Long> questionTypeIds) {
        if (CollectionUtils.isEmpty(questionTypeIds)) {
            return Collections.emptyList();
        }
        return questionMapper.countBySubjectAndQuestionTypeIds(subject, questionTypeIds);
    }

    @Override
    public Long getMaxId() {
        // 获取最大id
        return questionMapper.getMaxId();
    }

    @Override
    public List<QuestionJobVo> getQuestionJobVos(Long startId, int batchSize) {
        LambdaQueryWrapper<QuestionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(QuestionDO::getId, QuestionDO::getCorrectAnswerCount, QuestionDO::getTotalAnswerCount);
        queryWrapper.ge(QuestionDO::getId, startId);
        queryWrapper.orderByAsc(QuestionDO::getId);
        queryWrapper.last("limit " + batchSize);
        List<QuestionDO> questionDOs = questionMapper.selectList(queryWrapper);

        return BeanUtils.toBean(questionDOs, QuestionJobVo.class);
    }

    @Override
    public List<QuestionUnitCount> countByUnitIds(Set<Long> unitIds) {
        unitIds.remove(null);
        if (CollectionUtils.isEmpty(unitIds)) {
            return Collections.emptyList();
        }
        return questionMapper.countByUnitIds(unitIds);
    }

    @Override
    public PageResult<QuestionRespVO> queryQuestionPage(QuestionPageReqVO pageReqVO) {
        IPage<QuestionRespVO> queryQuestionPage = questionMapper.queryQuestionPage(new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize()), pageReqVO);
        return new PageResult<>(queryQuestionPage.getRecords(), queryQuestionPage.getTotal());
    }

    @Override
    public Long queryQuestionCount(QuestionPageReqVO pageReqVO) {
        return questionMapper.queryQuestionCount(pageReqVO);
    }

    @Override
    public List<QuestionTypeCountRespVO> getQuestionTypeCountRespVOByUnitIds(QuestionSearchReqVO reqVO) {
        return questionMapper.getQuestionTypeCountRespVOByUnitIds(reqVO);
    }

    @Override
    public Long countByChapterId(Long id) {
        return questionMapper.countByChapterId(id);
    }

    @Override
    public List<TextbookChapterQuestionRespVO> getTextbookChapterQuestions(QuestionSearchReqVO reqVO) {
        return questionMapper.getTextbookChapterQuestions(reqVO);
    }

    @Override
    public List<Long> selectUserPracticeQuestions(QuestionSearchReqVO reqVO) {
        return questionMapper.selectUserPracticeQuestions(reqVO);
    }

    @Override
    public QuestionRespVO getQuestionById(Long id) {
       return questionMapper.queryQuestionById(id);
    }

    @Override
    public List<Long> queryQuestionIdList() {
       return questionMapper.selectIdList();
    }

    @Override
    public List<QuestionTextbookCount> countByTextbookIds(Set<Long> textbookIds) {
        if (CollectionUtils.isEmpty(textbookIds)) {
            return Collections.emptyList();
        }
        return questionMapper.countByTextbookIds(textbookIds);
    }

    /**
     * 根据题目id列表查询题目
     *
     * @param idList ID列表
     * @return 题目列表
     */
    @Override
    public List<QuestionRespVO> queryQuestionByIdList(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return questionMapper.queryQuestionByIdList(idList);
    }

    @Override
    public List<QuestionJobVo> getQuestionJobVos(List<Long> questionIds) {
        LambdaQueryWrapper<QuestionDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(QuestionDO::getId, QuestionDO::getCorrectAnswerCount, QuestionDO::getTotalAnswerCount);
        queryWrapper.in(QuestionDO::getId, questionIds);
        queryWrapper.orderByAsc(QuestionDO::getId);
        List<QuestionDO> questionDOs = questionMapper.selectList(queryWrapper);

        return BeanUtils.toBean(questionDOs, QuestionJobVo.class);
    }

    @Override
    public List<Long> randomQuestionIds(int level, int subject, int batchSize) {

        return questionMapper.getRandomQuestionIds(level, subject, batchSize);
    }


}