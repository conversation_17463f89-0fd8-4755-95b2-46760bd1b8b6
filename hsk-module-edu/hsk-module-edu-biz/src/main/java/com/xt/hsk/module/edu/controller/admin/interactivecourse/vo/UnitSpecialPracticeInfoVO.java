package com.xt.hsk.module.edu.controller.admin.interactivecourse.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.IsShowEnum;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 互动课关联专项练习信息 vo
 *
 * <AUTHOR>
 * @since 2025/06/30
 */
@Data
public class UnitSpecialPracticeInfoVO implements VO {
    /**
     * 专项练习ID
     */
    private Long id;

    /**
     * 专项练习名称-中文
     */
    private String nameCn;

    /**
     * 练习类型
     *
     */
    private Integer type;

    /**
     * HSK等级
     */
    @Trans(type = TransType.ENUM, key = "code", target = HskEnum.class, ref = "hskLevelDesc")
    private Integer hskLevel;
    /**
     * HSK等级描述
     */
    private String hskLevelDesc;

    /**
     * 是否展示 1展示 0不展示
     */
    @Trans(type = TransType.ENUM, key = "code", target = IsShowEnum.class, ref = "isShowDesc")
    private Integer isShow;
    /**
     * 是否展示 1展示 0不展示
     */
    private String isShowDesc;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 专项练习ID
     */
    private Long specialExerciseId;

    /**
     * 题目列表
     */
    private List<String> questionList;

    Map<String, Object> transMap = new HashMap<>();
}
