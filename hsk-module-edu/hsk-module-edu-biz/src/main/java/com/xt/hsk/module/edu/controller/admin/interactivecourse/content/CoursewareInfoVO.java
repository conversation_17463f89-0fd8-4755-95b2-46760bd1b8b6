package com.xt.hsk.module.edu.controller.admin.interactivecourse.content;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 课件信息
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
public class CoursewareInfoVO {

    /**
     * 课件ID
     */
    private Long id;

    /**
     * 资料名称
     */
    private String materialName;

    /**
     * 序号
     */
    @NotNull(message = "课件序号不能为空")
    private Integer sort;

    /**
     * 资料URL
     */
    @NotEmpty(message = "资料URL不能为空")
    private String materialUrl;

    /**
     * 资料大小（字节）
     */
    @NotNull(message = "资料大小不能为空")
    private Long materialSize;


} 