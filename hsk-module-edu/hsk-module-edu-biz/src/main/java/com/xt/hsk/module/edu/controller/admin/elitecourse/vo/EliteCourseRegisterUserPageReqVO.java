package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;

import java.util.List;

/**
 * 精品课程登记用户 分页 req vo
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
public class EliteCourseRegisterUserPageReqVO extends PageParam {

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 手机区号
     */
    private String countryCode;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 课时 ID
     */
    private Long classHourId;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * ID列表
     */
    private List<Long> ids;

}