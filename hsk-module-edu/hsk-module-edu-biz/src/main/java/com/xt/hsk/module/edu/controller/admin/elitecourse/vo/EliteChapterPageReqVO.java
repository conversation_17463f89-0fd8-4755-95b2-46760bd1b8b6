package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 精品课-章节 req vo
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EliteChapterPageReqVO extends PageParam {

    /**
     * 章节名称-中文
     */
    private String chapterNameCn;

    /**
     * 章节名称-英文
     */
    private String chapterNameEn;

    /**
     * 章节名称-其他
     */
    private String chapterNameOt;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}