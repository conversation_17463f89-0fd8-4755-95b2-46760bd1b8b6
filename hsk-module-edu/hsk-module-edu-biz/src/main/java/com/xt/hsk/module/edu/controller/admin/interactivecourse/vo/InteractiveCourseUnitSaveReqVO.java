package com.xt.hsk.module.edu.controller.admin.interactivecourse.vo;

import com.xt.hsk.module.edu.controller.admin.interactivecourse.content.CourseVideoLinkVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.content.CoursewareInfoVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.content.VocabularyInfoVO;
import com.xt.hsk.module.edu.enums.interactivecourse.UnitQuestionSourceTypeEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * 互动课单元创建/更新参数类
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
public class InteractiveCourseUnitSaveReqVO {

    /**
     * 课程单元ID
     */
    private Long id;

    /**
     * 课程ID
     */
    @NotNull(message = "课程ID不能为空")
    private Long courseId;

    /**
     * 单元名称-中文
     */
    @NotEmpty(message = "单元名称不能为空")
    private String unitNameCn;

    /**
     * 单元名称-英文
     */
    private String unitNameEn;

    /**
     * 单元名称-其他
     */
    private String unitNameOt;

    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;

    /**
     * 展示状态 0-隐藏 1-显示
     */
    @NotNull(message = "请选择展示状态")
    private Boolean displayStatus;

    /**
     * 单元类型 1-视频 2-专项练习 3-真题练习
     * @see UnitQuestionSourceTypeEnum
     */
    @NotNull(message = "题目来源不能为空")
    private Integer questionSource;

    /**
     * 封面URL
     */
    private String coverUrl;

    /**
     * 推荐学习时长(秒)
     */
    @NotNull(message = "推荐学习时长不能为空")
    private Integer recommendedDuration;

    /**
     * 视频信息（当unitType=1时必填）
     */
    private VideoInfoVO videoInfo;

    /**
     * 专项练习信息（当unitType=2时必填）
     */
    private List<SpecialPracticeInfoVO> specialPracticeInfo;

    /**
     * 真题练习信息（当unitType=3时必填）
     */
    private List<QuestionInfoVO> realExamInfo;

    /**
     * 课件列表（可选，当视频类型=1时）
     */
    private List<CoursewareInfoVO> coursewareList;

    /**
     * 生词列表（可选，当视频类型=2时）
     */
    private List<VocabularyInfoVO> vocabularyList;

    /**
     * 视频信息VO
     */
    @Data
    public static class VideoInfoVO {

        /**
         * 视频表ID
         */
        private Long id;

        /**
         * 视频名称-中文
         */
        @NotEmpty(message = "视频名称不能为空")
        private String videoNameCn;

        /**
         * 视频名称-英文
         */
        private String videoNameEn;

        /**
         * 视频名称-其他
         */
        private String videoNameOt;

        /**
         * 视频类型 1-视频带课件 2-视频带生词
         *
         * @see com.xt.hsk.module.edu.enums.interactivecourse.VideoTypeEnum
         */
        @NotNull(message = "视频类型不能为空")
        private Integer videoType;

        /**
         * 视频尺寸比例 1-9:16 2-16:9
         *
         * @see com.xt.hsk.module.edu.enums.interactivecourse.VideoAspectRatioEnum
         */
        @NotNull(message = "视频尺寸比例不能为空")
        private Integer aspectRatio;

        /**
         * 视频链接列表
         */
        private List<CourseVideoLinkVO> videoLinkList;
    }

    /**
     * 专项练习信息VO
     */
    @Data
    public static class SpecialPracticeInfoVO {

        /**
         * 关联表id
         */
        private Long id;
        /**
         * 练习组ID
         */
        @NotNull(message = "练习组ID不能为空")
        private Long practiceGroupId;
        /**
         * 排序
         */
        @NotNull(message = "排序不能为空")
        private Integer sort;
    }

    /**
     * 真题练习信息VO
     */
    @Data
    public static class QuestionInfoVO {

        /**
         * 关联表id
         */
        private Long id;
        /**
         * 真题练习ID
         */
        @NotNull(message = "题目练习ID不能为空")
        private Long questionId;
        /**
         * 排序
         */
        @NotNull(message = "排序不能为空")
        private Integer sort;
    }

} 