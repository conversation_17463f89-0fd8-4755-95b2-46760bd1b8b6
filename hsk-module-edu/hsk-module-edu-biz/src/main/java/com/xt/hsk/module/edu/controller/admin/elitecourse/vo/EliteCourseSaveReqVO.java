package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.xt.hsk.module.edu.enums.elitecourse.ClassHourNumberStatusEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseListingMethodEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseListingStatusEnum;
import com.xt.hsk.module.edu.enums.elitecourse.EliteCourseTypeEnum;
import com.xt.hsk.module.edu.enums.elitecourse.LearningValidityPeriodEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 精品课 Save Req Vo
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
public class EliteCourseSaveReqVO {

    /**
     * 精品课ID
     */
    private Long id;

    /**
     * HSK等级（1-6）
     */
    @NotNull(message = "请选择HSK等级")
    private Integer hskLevel;

    /**
     * 一级分类id
     */
    private Long primaryCategoryId;

    /**
     * 二级分类id
     */
    private Long secondaryCategoryId;

    /**
     * 课程名称-中文
     */
    @NotBlank(message = "课程名称不能为空")
    private String courseNameCn;

    /**
     * 课程名称-英文
     */
    private String courseNameEn;

    /**
     * 课程名称-其他
     */
    private String courseNameOt;

    /**
     * 课程封面大图URL
     */
    @NotBlank(message = "请上传课程封面大图")
    private String coverUrlLarge;

    /**
     * 课程封面小图URL
     */
    @NotBlank(message = "请上传课程封面小图")
    private String coverUrlSmall;

    /**
     * 课时数状态 1：课程大纲课时数 2：自定义课时
     * @see ClassHourNumberStatusEnum
     */
    @NotNull(message = "请选择课时数状态")
    private Integer classHourNumberStatus;

    /**
     * 自定义课时数
     */
    private Integer customClassHourNumber;

    /**
     * 划线价格(人民币)
     */
    private BigDecimal originalPriceCn;

    /**
     * 售卖价格(人民币)
     */
    private BigDecimal sellingPriceCn;

    /**
     * 划线价格(美元)
     */
    private BigDecimal originalPriceEn;

    /**
     * 售卖价格(美元)
     */
    private BigDecimal sellingPriceEn;

    /**
     * 划线价格(越南盾)
     */
    private BigDecimal originalPriceOt;

    /**
     * 售卖价格(越南盾)
     */
    private BigDecimal sellingPriceOt;

    /**
     * 课程详情内容
     */
    private String courseDetail;

    /**
     * 上架方式 1：立即上架 2：定时上架 3：暂不上架
     * @see EliteCourseListingMethodEnum
     */
    @NotNull(message = "请选择上架方式")
    private Integer listingMethod;

    /**
     * 上架状态 1：上架 2：下架 3：待上架
     * @see EliteCourseListingStatusEnum
     */
    private Integer listingStatus;

    /**
     * 上架时间
     */
    private LocalDateTime listingTime;

    /**
     * 销售基数
     */
    private Integer salesBase;

    /**
     * 学习有效期 1：长期有效 2：按截止日期 3：按天数
     * @see LearningValidityPeriodEnum
     */
    @NotNull(message = "请选择学习有效期")
    private Integer learningValidityPeriod;

    /**
     * 截至日期
     */
    private LocalDateTime deadline;

    /**
     * 有效天数
     */
    private Integer effectiveDays;

    /**
     * 生效模式
     */
    private Integer effectModel;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 是否展示 1展示 0不展示
     */
    @NotNull(message = "请选择展示状态")
    private Integer isShow;

    /**
     * 报名人数
     */
    private Integer enrollmentCount;

    /**
     * 类型 1.普通课程 2.公开课
     * @see EliteCourseTypeEnum
     */
    private Integer type;

    /**
     * 讲师id列表
     */
    private List<Long> teacherIdList;

    /**
     * 售卖类型 1.付费  2.免费
     */
    private Integer saleType;

}