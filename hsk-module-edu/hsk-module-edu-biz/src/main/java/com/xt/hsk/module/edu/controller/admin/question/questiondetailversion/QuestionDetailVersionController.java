package com.xt.hsk.module.edu.controller.admin.question.questiondetailversion;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.question.questiondetailversion.vo.QuestionDetailVersionPageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.questiondetailversion.vo.QuestionDetailVersionRespVO;
import com.xt.hsk.module.edu.controller.admin.question.questiondetailversion.vo.QuestionDetailVersionSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetailversion.QuestionDetailVersionDO;
import com.xt.hsk.module.edu.service.question.questiondetailversion.QuestionDetailVersionManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 题目详情表版本库")
@RestController
@RequestMapping("/edu/question-detail-version")
@Validated
public class QuestionDetailVersionController {

    @Resource
    private QuestionDetailVersionManager questionDetailVersionManager;

    @PostMapping("/create")
    @Operation(summary = "创建题目详情表版本库")
    @PreAuthorize("@ss.hasPermission('edu:question-detail-version:create')")
    public CommonResult<Long> createQuestionDetailVersion(@Valid @RequestBody QuestionDetailVersionSaveReqVO createReqVO) {
        return success(questionDetailVersionManager.createQuestionDetailVersion(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新题目详情表版本库")
    @PreAuthorize("@ss.hasPermission('edu:question-detail-version:update')")
    public CommonResult<Boolean> updateQuestionDetailVersion(@Valid @RequestBody QuestionDetailVersionSaveReqVO updateReqVO) {
        questionDetailVersionManager.updateQuestionDetailVersion(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除题目详情表版本库")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:question-detail-version:delete')")
    public CommonResult<Boolean> deleteQuestionDetailVersion(@RequestParam("id") Long id) {
        questionDetailVersionManager.deleteQuestionDetailVersion(id);
        return success(true);
    }

    @PostMapping("/get")
    @Operation(summary = "获得题目详情表版本库")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:question-detail-version:query')")
    public CommonResult<QuestionDetailVersionRespVO> getQuestionDetailVersion(@RequestParam("id") Long id) {
        QuestionDetailVersionDO questionDetailVersion = questionDetailVersionManager.getQuestionDetailVersion(id);
        return success(BeanUtils.toBean(questionDetailVersion, QuestionDetailVersionRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得题目详情表版本库分页")
    @PreAuthorize("@ss.hasPermission('edu:question-detail-version:query')")
    public CommonResult
            <PageResult
                    <QuestionDetailVersionRespVO>> getQuestionDetailVersionPage(@Valid
                                                                                @RequestBody QuestionDetailVersionPageReqVO pageReqVO) {
        PageResult<QuestionDetailVersionDO> pageResult = questionDetailVersionManager.getQuestionDetailVersionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, QuestionDetailVersionRespVO.class));
    }


}