package com.xt.hsk.module.edu.controller.admin.interactivecourse.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 互动课基本信息 vo
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InteractiveCourseBaseInfoRespVO {

    /**
     * 课程ID
     */
    private Long id;

    /**
     * 课程类型
     */
    private Integer type;

    /**
     * 课程名称
     */
    private String courseNameCn;

    /**
     * 课程名称-英文
     */
    private String courseNameEn;

    /**
     * 课程名称-其他
     */
    private String courseNameOt;
    /**
     * 资源ID
     */
    private Long resourceId;
} 