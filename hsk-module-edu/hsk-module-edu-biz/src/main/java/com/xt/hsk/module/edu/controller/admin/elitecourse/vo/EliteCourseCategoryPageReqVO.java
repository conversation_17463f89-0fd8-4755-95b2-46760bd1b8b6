package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 精品课-分类 页面 req vo
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EliteCourseCategoryPageReqVO extends PageParam {

    /**
     * 分类名称-中文
     */
    private String nameCn;

    /**
     * 分类名称-英文
     */
    private String nameEn;

    /**
     * 分类名称-其他
     */
    private String nameOt;

    /**
     * 一等分类id
     */
    private Long parentId;

    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 类型 1.普通课程 2.公开课
     */
    private Integer type;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}