package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 精品课-课程登记 页面 req vo
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EliteCourseRegisterPageReqVO extends PageParam {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 课程类型：1.普通课程 2.公开课
     */
    private Integer courseType;

    /**
     * 预约或购买时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] enrollmentTime;

    /**
     * 报名途径：1.线上购买 2.线下报名(后台)
     */
    private Integer registerType;

    /**
     * 购买课程订单号
     */
    private String orderNumber;

    /**
     * 订单详情ID
     */
    private Long orderDetailId;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 课程状态 1 正常 2 未开通 3 手动开启
     */
    private Integer courseRegisterStatus;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}