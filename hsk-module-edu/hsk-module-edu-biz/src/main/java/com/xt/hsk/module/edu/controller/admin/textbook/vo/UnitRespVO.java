package com.xt.hsk.module.edu.controller.admin.textbook.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.xt.hsk.framework.common.enums.DifficultyLevelEnum;
import lombok.*;

import java.time.LocalDateTime;

@Data
public class UnitRespVO {


    /**
     * 主键
     */
    private Long id;


    /**
     * 所属章节
     */
    private Long chapterId;


    /**
     * 教材id
     */
    private Long textbookId;


    /**
     * 题型描述
     */
    private String typeDesc;
    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;

    /**
     * 单元名称
     */
    private String unitNameCn;


    /**
     * 单元名称
     */
    private String unitNameEn;


    /**
     * 单元名称
     */
    private String unitNameOt;


    /**
     * 排序序号
     */
    private Integer sort;


//    /**
//     * 难度等级 1 简单 2中等 3 困难
//     */
//    @Trans(type = TransType.ENUM, target = DifficultyLevelEnum.class,ref = "difficultyLevelDesc")
//    private Integer difficultyLevel;
//    /**
//     * 难度等级 1 简单 2中等 3 困难
//     */
//    private String difficultyLevelDesc;
    /**
     * 科目（1=听力，2=阅读，4=书写）
     */

    private Integer subject;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}