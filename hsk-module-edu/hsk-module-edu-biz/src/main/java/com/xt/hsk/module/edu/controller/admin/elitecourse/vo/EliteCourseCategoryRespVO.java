package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 精品课-分类 RESP VO
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
public class EliteCourseCategoryRespVO implements VO {


    /**
     * 精品课分类ID
     */
    private Long id;


    /**
     * 分类名称-中文
     */
    private String nameCn;


    /**
     * 分类名称-英文
     */
    private String nameEn;


    /**
     * 分类名称-其他
     */
    private String nameOt;


    /**
     * 一等分类id
     */
    private Long parentId;


    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;


    /**
     * 排序序号
     */
    private Integer sort;


    /**
     * 类型 1.普通课程 2.公开课
     */
    private Integer type;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最近更新人ID
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "updaterName")
    private String updater;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 课程数量
     */
    private Long courseCount;
}