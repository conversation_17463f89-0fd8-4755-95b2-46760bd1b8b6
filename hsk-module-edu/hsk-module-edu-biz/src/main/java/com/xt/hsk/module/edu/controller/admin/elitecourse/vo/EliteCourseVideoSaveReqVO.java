package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 精品课视频 save req vo
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
public class EliteCourseVideoSaveReqVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 视频/回放ID
     */
    @NotNull(message = "视频/回放ID不能为空")
    private Long videoId;

    /**
     * 房间ID
     */
    private Long roomId;

    /**
     * 视频名称-中文
     */
    @NotEmpty(message = "视频名称-中文不能为空")
    private String nameCn;

    /**
     * 视频名称-英文
     */
    private String nameEn;

    /**
     * 视频名称-其他
     */
    private String nameOt;

    /**
     * 视频的url
     */
    private String videoUrl;

    /**
     * 封面图片的url
     */
    private String prefaceUrl;

    /**
     * 添加/回放生成时间
     */
    private LocalDateTime videoCreateTime;

    /**
     * 视频大小，单位：字节
     */
    @NotNull(message = "视频大小，单位：字节不能为空")
    private Long totalSize;

    /**
     * 视频时长单位：秒
     */
    @NotNull(message = "视频时长单位：秒不能为空")
    private Integer length;

    /**
     * 1.录播
     */
    private Integer type;

}