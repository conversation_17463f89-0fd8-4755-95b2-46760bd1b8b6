package com.xt.hsk.module.edu.controller.admin.userquestionanswerdata;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;

import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.xt.hsk.module.edu.controller.admin.userquestionanswerdata.vo.*;
import com.xt.hsk.module.edu.dal.dataobject.userquestionanswerdata.UserQuestionAnswerDataDO;
import com.xt.hsk.module.edu.service.userquestionanswerrecord.userquestionanswerdata.UserQuestionAnswerDataManager;

@Tag(name = "管理后台 - 用户题目作答数据")
@RestController
@RequestMapping("/edu/user-question-answer-data")
@Validated
public class UserQuestionAnswerDataController {

    @Resource
    private UserQuestionAnswerDataManager userQuestionAnswerDataManager;

    @PostMapping("/create")
    @Operation(summary = "创建用户题目作答数据")
    @PreAuthorize("@ss.hasPermission('edu:user-question-answer-data:create')")
    public CommonResult<Long> createUserQuestionAnswerData(@Valid @RequestBody UserQuestionAnswerDataSaveReqVO createReqVO) {
        return success(userQuestionAnswerDataManager.createUserQuestionAnswerData(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户题目作答数据")
    @PreAuthorize("@ss.hasPermission('edu:user-question-answer-data:update')")
    public CommonResult<Boolean> updateUserQuestionAnswerData(@Valid @RequestBody UserQuestionAnswerDataSaveReqVO updateReqVO) {
        userQuestionAnswerDataManager.updateUserQuestionAnswerData(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除用户题目作答数据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:user-question-answer-data:delete')")
    public CommonResult<Boolean> deleteUserQuestionAnswerData(@RequestParam("id") Long id) {
        userQuestionAnswerDataManager.deleteUserQuestionAnswerData(id);
        return success(true);
    }

    @PostMapping("/get")
    @Operation(summary = "获得用户题目作答数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:user-question-answer-data:query')")
    public CommonResult<UserQuestionAnswerDataRespVO> getUserQuestionAnswerData(@RequestParam("id") Long id) {
        UserQuestionAnswerDataDO userQuestionAnswerData = userQuestionAnswerDataManager.getUserQuestionAnswerData(id);
        return success(BeanUtils.toBean(userQuestionAnswerData, UserQuestionAnswerDataRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得用户题目作答数据分页")
    @PreAuthorize("@ss.hasPermission('edu:user-question-answer-data:query')")
    public CommonResult
            <PageResult
                    <UserQuestionAnswerDataRespVO>> getUserQuestionAnswerDataPage(@Valid
                                                                                  @RequestBody UserQuestionAnswerDataPageReqVO pageReqVO) {
        PageResult<UserQuestionAnswerDataDO> pageResult = userQuestionAnswerDataManager.getUserQuestionAnswerDataPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, UserQuestionAnswerDataRespVO.class));
    }


}