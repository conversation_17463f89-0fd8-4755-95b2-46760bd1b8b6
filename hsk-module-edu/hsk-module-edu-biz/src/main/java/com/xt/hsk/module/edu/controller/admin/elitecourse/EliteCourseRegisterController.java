package com.xt.hsk.module.edu.controller.admin.elitecourse;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseRegisterSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseUserExportReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseUserRespVO;
import com.xt.hsk.module.edu.manager.elitecourse.EliteCourseRegisterManager;
import com.xt.hsk.module.infra.api.export.ExportTaskApi;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 精品课-课程登记 控制器
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@RestController
@Validated
@Slf4j
@RequestMapping("/edu/elite-course-register")
public class EliteCourseRegisterController {

    @Resource
    private EliteCourseRegisterManager eliteCourseRegisterManager;

    @Resource
    private ExportTaskApi exportTaskApi;

    /**
     * 精品课课程登记添加用户
     */
    @PostMapping("/add-user")
    @LogRecord(type = LogRecordType.ELITE_COURSE_REGISTER_TYPE, bizNo = "{{#createReqVO.courseId}}", success = "精品课课程登记添加用户")
    @PreAuthorize("@ss.hasPermission('edu:elite-course-register:create')")
    public CommonResult<Boolean> createEliteCourseRegister(@Valid @RequestBody EliteCourseRegisterSaveReqVO createReqVO) {
        eliteCourseRegisterManager.addUser(createReqVO);
        return success(true);
    }

    /**
     * 删除精品课课程登记
     */
    @DeleteMapping("/delete")
    @LogRecord(type = LogRecordType.ELITE_COURSE_REGISTER_TYPE, bizNo = "{{#id}}", success = "删除精品课课程登记")
    @PreAuthorize("@ss.hasPermission('edu:elite-course-register:delete')")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        eliteCourseRegisterManager.delete(id);
        return success(true);
    }

    /**
     * 分页获取课程学员列表
     */
    @PostMapping("/page")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:query')")
    public CommonResult<PageResult<EliteCourseUserRespVO>> getCourseUserList(@RequestBody EliteCourseStudyPageReqVO pageReqVO) {
        return success(eliteCourseRegisterManager.getCourseUserList(pageReqVO));
    }

    /**
     * 导出精品课学员数据
     */
    @LogRecord(type = LogRecordType.ELITE_COURSE_TYPE, bizNo = "{{#taskId}}", success = "创建精品课学员异步导出任务")
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:export')")
    public CommonResult<Long> exportEliteCourseUsers(@RequestBody @Valid EliteCourseUserExportReqVO exportReqVO) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 将导出请求参数和任务名称一起传递
            ExportTaskParams taskParams = new ExportTaskParams();
            taskParams.setTaskName(exportReqVO.getTaskName());
            taskParams.setQueryParams(exportReqVO);

            String params = objectMapper.writeValueAsString(taskParams);
            Long taskId = exportTaskApi.createExportTask(exportReqVO.getTaskName(),
                    ExportTaskTypeEnum.ELITE_COURSE_USER, params);
            LogRecordContext.putVariable("taskId", taskId);
            return success(taskId);
        } catch (Exception e) {
            log.error("创建精品课学员导出任务失败", e);
            return CommonResult.error(500, "创建导出任务失败：" + e.getMessage());
        }
    }


}