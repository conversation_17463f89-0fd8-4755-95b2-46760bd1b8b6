package com.xt.hsk.module.edu.controller.admin.textbook.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import jakarta.validation.constraints.*;

@Data
public class TextbookSaveReqVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 教程名 中文
     */
    @NotEmpty(message = "教程名 中文不能为空")
    private String nameCn;

    /**
     * 教程名 英文
     */
    private String nameEn;

    /**
     * 教程名 其他语种
     */
    private String nameOt;

    /**
     * 排序序号
     */
    @NotNull(message = "排序序号不能为空")
    private Integer sort;

    /**
     * 是否展示 1展示 0不展示
     */
    @NotNull(message = "是否展示不能为空")
    private Integer isShow;

    /**
     * 教材分类 1 考场真题 2 模拟题
     */
    @NotNull(message = "教材分类不能为空")
    private Integer type;

    /**
     * 真题数量
     */
    private Integer questionNumber;

    /**
     * HSK等级（1-6）
     */
    @NotNull(message = "所属等级不能为空")
    private Integer hskLevel;

}