package com.xt.hsk.module.edu.controller.admin.interactivecourse.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseTypeEnum;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 互动课程 Resp VO
 *
 * <AUTHOR>
 * @since 2025/05/23
 */
@Data
public class InteractiveCourseRespVO implements VO {

    /**
     * 课程ID
     */
    private Long id;

    /**
     * 课程类型
     */
    @Trans(type = TransType.ENUM, key = "code", target = InteractiveCourseTypeEnum.class, ref = "typeDesc")
    private Integer type;

    /**
     * 课程类型描述
     */
    private String typeDesc;

    /**
     * 课程名称
     */
    private String courseNameCn;

    /**
     * 课程名称-英文
     */
    private String courseNameEn;

    /**
     * 课程名称-其他
     */
    private String courseNameOt;

    /**
     * 展示状态 0-隐藏 1-显示
     */
    private Integer displayStatus;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * HSK等级
     */
    @Trans(type = TransType.ENUM, key = "code", target = HskEnum.class, ref = "hskLevelDesc")
    private Integer hskLevel;
    /**
     * HSK等级描述
     */
    private String hskLevelDesc;

    /**
     * 封面URL
     */
    private String coverUrl;

    /**
     * 学习基数
     */
    private Integer learningBase;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最近更新人ID
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO",
        fields = "nickname", ref = "updaterName")
    private String updater;
    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 单元数量
     */
    private Long unitCount;
}