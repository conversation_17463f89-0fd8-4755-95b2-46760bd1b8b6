package com.xt.hsk.module.edu.controller.admin.elitecourse;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourStudyStatsExportReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteClassHourStudySummaryRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseRegisterUserPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyDetailPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyDetailRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyStatsExportReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyStatsPageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseStudyStatsRespVO;
import com.xt.hsk.module.edu.manager.elitecourse.EliteCourseStudyStatsManager;
import com.xt.hsk.module.infra.api.export.ExportTaskApi;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 精品课程学习统计 管理后台 控制器
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@RestController
@Validated
@Slf4j
@RequestMapping("/edu/elite-course-study-stats")
public class EliteCourseStudyStatsController {

    @Resource
    private EliteCourseStudyStatsManager eliteCourseStudyStatsManager;

    @Resource
    private ExportTaskApi exportTaskApi;

    /**
     * 分页获取课程学习统计
     */
    @PostMapping("/page")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:query')")
    public CommonResult<PageResult<EliteCourseStudyStatsRespVO>> getCourseStudyStatsPage(@RequestBody EliteCourseStudyStatsPageReqVO pageReqVO) {
        return success(eliteCourseStudyStatsManager.getCourseStudyStatsPage(pageReqVO));
    }

    /**
     * 分页获取用户学习明细列表
     */
    @PostMapping("/detail/page")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:query')")
    public CommonResult<PageResult<EliteCourseStudyDetailRespVO>> getUserStudyDetailPage(@Valid @RequestBody EliteCourseStudyDetailPageReqVO pageReqVO) {
        return success(eliteCourseStudyStatsManager.getUserStudyDetailPage(pageReqVO));
    }

    /**
     * 获取精品课课时学习汇总数据
     */
    @GetMapping("/class-hour/summary")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:query')")
    public CommonResult<EliteClassHourStudySummaryRespVO> getClassHourStudySummary(@RequestParam("classHourId") Long classHourId) {
        return success(eliteCourseStudyStatsManager.getClassHourStudySummary(classHourId));
    }

    /**
     * 分页获取课时学习统计
     */
    @PostMapping("/class-hour/page")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:query')")
    public CommonResult<PageResult<EliteCourseStudyStatsRespVO>> getClassHourStudyStatsPage(@RequestBody EliteCourseRegisterUserPageReqVO pageReqVO) {
        return success(eliteCourseStudyStatsManager.getClassHourStudyStatsPage(pageReqVO));
    }

    /**
     * 导出精品课学习统计数据
     */
    @LogRecord(type = LogRecordType.ELITE_COURSE_TYPE, bizNo = "{{#taskId}}", success = "创建精品课学习统计异步导出任务")
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:export')")
    public CommonResult<Long> exportEliteCourseStudyStats(@RequestBody @Valid EliteCourseStudyStatsExportReqVO exportReqVO) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 将导出请求参数和任务名称一起传递
            ExportTaskParams taskParams = new ExportTaskParams();
            taskParams.setTaskName(exportReqVO.getTaskName());
            taskParams.setQueryParams(exportReqVO);

            String params = objectMapper.writeValueAsString(taskParams);
            Long taskId = exportTaskApi.createExportTask(exportReqVO.getTaskName(),
                    ExportTaskTypeEnum.ELITE_COURSE_STUDY_STATS, params);
            LogRecordContext.putVariable("taskId", taskId);
            return success(taskId);
        } catch (Exception e) {
            log.error("创建精品课学习统计导出任务失败", e);
            return CommonResult.error(500, "创建导出任务失败：" + e.getMessage());
        }
    }

    /**
     * 导出精品课课时学习统计数据
     */
    @LogRecord(type = LogRecordType.ELITE_COURSE_TYPE, bizNo = "{{#taskId}}", success = "创建精品课课时学习统计异步导出任务")
    @PostMapping("/class-hour/export")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:export')")
    public CommonResult<Long> exportClassHourStudyStats(@RequestBody @Valid EliteClassHourStudyStatsExportReqVO exportReqVO) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 将导出请求参数和任务名称一起传递
            ExportTaskParams taskParams = new ExportTaskParams();
            taskParams.setTaskName(exportReqVO.getTaskName());
            taskParams.setQueryParams(exportReqVO);

            String params = objectMapper.writeValueAsString(taskParams);
            Long taskId = exportTaskApi.createExportTask(exportReqVO.getTaskName(),
                    ExportTaskTypeEnum.ELITE_CLASS_HOUR_STUDY_STATS, params);
            LogRecordContext.putVariable("taskId", taskId);
            return success(taskId);
        } catch (Exception e) {
            log.error("创建精品课课时学习统计导出任务失败", e);
            return CommonResult.error(500, "创建导出任务失败：" + e.getMessage());
        }
    }
}