package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 精品课-章节保存 req vo
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
public class EliteChapterSaveReqVO {

    /**
     * 章节ID
     */
    private Long id;

    /**
     * 章节名称-中文
     */
    private String chapterNameCn;

    /**
     * 章节名称-英文
     */
    private String chapterNameEn;

    /**
     * 章节名称-其他
     */
    private String chapterNameOt;

    /**
     * 课程id
     */
    @NotNull(message = "课程id不能为空")
    private Long courseId;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 章节列表
     */
    @NotEmpty(message = "章节名称不能为空")
    private List<EliteChapterSaveReqVO> chapterList;

}