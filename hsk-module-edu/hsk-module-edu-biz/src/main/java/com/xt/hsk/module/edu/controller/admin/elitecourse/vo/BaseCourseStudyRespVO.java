package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 基础课程学习 Resp Vo
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
public class BaseCourseStudyRespVO {

    /**
     * 课时时长
     */
    private Integer classHourLength;

    /**
     * 课时时长格式化字符串（例如："1小时30分"）
     */
    private String classHourLengthStr;

    /**
     * 学习时长，单位：秒
     */
    private Integer studyLength;

    /**
     * 学习时长格式化字符串（例如："1小时30分"）
     */
    private String studyLengthStr;

    /**
     * 学习进度
     */
    private Double studyProgress;

    /**
     * 学习进度格式化字符串（例如："75%"）
     */
    private String studyProgressStr;

    /**
     * 第一次学习时间（首次学习时间）
     */
    private LocalDateTime firstStudyTime;

    /**
     * 最后一次学习时间（最近一次学习）
     */
    private LocalDateTime lastStudyTime;

    /**
     * 学习次数
     */
    private Integer studyCount;

}
