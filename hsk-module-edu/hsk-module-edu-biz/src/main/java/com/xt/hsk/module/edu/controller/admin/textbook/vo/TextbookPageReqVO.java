package com.xt.hsk.module.edu.controller.admin.textbook.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TextbookPageReqVO extends PageParam {

    /**
     * 教程名 中文
     */
    private String nameCn;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 教材分类 考场真题，模拟题
     */
    private Integer type;


    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;


}