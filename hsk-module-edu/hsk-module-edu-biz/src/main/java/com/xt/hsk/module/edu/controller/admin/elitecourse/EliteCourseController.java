package com.xt.hsk.module.edu.controller.admin.elitecourse;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseDirectoryRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseExportReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseOverviewRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCoursePageReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseRespVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteDeleteCheckRespVO;
import com.xt.hsk.module.edu.manager.elitecourse.EliteCourseManager;
import com.xt.hsk.module.infra.api.export.ExportTaskApi;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 精品课 控制器
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@RestController
@Validated
@Slf4j
@RequestMapping("/edu/elite-course")
public class EliteCourseController {

    @Resource
    private EliteCourseManager eliteCourseManager;

    @Resource
    private ExportTaskApi exportTaskApi;

    /**
     * 创建精品课
     */
    @PostMapping("/create")
    @LogRecord(type = LogRecordType.ELITE_COURSE_TYPE,
        subType = "创建精品课", bizNo = "{{#courseId}}",
        success = "创建精品课：【{{#course.courseName}}】，分类：{{#categoryName}}")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:create')")
    public CommonResult<Long> createEliteCourse(@Valid @RequestBody EliteCourseSaveReqVO createReqVO) {
        return success(eliteCourseManager.createEliteCourse(createReqVO));
    }

    /**
     * 更新精品课
     */
    @PutMapping("/update")
    @LogRecord(type = LogRecordType.ELITE_COURSE_TYPE, subType = "修改精品课",
        bizNo = "{{#updateReqVO.id}}",
        success = "修改精品课：【{{#course.courseName}}】，分类：{{#categoryName}}")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:update')")
    public CommonResult<Boolean> updateEliteCourse(@Valid @RequestBody EliteCourseSaveReqVO updateReqVO) {
        eliteCourseManager.updateEliteCourse(updateReqVO);
        return success(true);
    }

    /**
     * 删除精品课
     *
     * @param id 精品课id
     */
    @DeleteMapping("/delete")
    @LogRecord(type = LogRecordType.ELITE_COURSE_TYPE,
        subType = "删除精品课",
        bizNo = "{{#id}}",
        success = "删除精品课：【{{#course.courseName}}】，分类：{{#categoryName}}")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:delete')")
    public CommonResult<Boolean> deleteEliteCourse(@RequestParam("id") Long id) {
        eliteCourseManager.deleteEliteCourse(id);
        return success(true);
    }

    /**
     * 检查精品课是否可以删除
     *
     * @param id 精品课id
     */
    @GetMapping("/check-delete")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:query')")
    public CommonResult<EliteDeleteCheckRespVO> checkCourseCanDelete(@RequestParam("id") Long id) {
        return success(eliteCourseManager.checkCourseCanDelete(id));
    }

    /**
     * 根据id获取精品课
     *
     * @param id 精品课id
     */
    @PostMapping("/get")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:query')")
    public CommonResult<EliteCourseRespVO> getEliteCourse(@RequestParam("id") Long id) {
        return success(eliteCourseManager.getEliteCourse(id));
    }

    /**
     * 分页获取精品课
     */
    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('edu:elite-course:query')")
    public CommonResult<PageResult<EliteCourseRespVO>> getEliteCoursePage(@Valid @RequestBody EliteCoursePageReqVO pageReqVO) {
        return success(eliteCourseManager.getEliteCoursePage(pageReqVO));
    }

    /**
     * 获取课程目录
     */
    @GetMapping("/directory")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:query')")
    public CommonResult<List<EliteCourseDirectoryRespVO>> getCourseDirectory(@RequestParam("courseId") Long courseId) {
        return success(eliteCourseManager.getCourseDirectory(courseId));
    }

    /**
     * 显示/隐藏精品课
     *
     * @param id 精品课id
     */
    @PutMapping("/update-status")
    @LogRecord(type = LogRecordType.ELITE_COURSE_TYPE, subType = "更新精品课显示状态", bizNo = "{{#id}}", success = "{{#statusText}}精品课：【{{#course.courseName}}】")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:update')")
    public CommonResult<Boolean> updateStatus(@RequestParam("id") Long id) {
        eliteCourseManager.updateStatus(id);
        return success(true);
    }

    /**
     * 上架/下架精品课
     *
     * @param id 精品课id
     */
    @PutMapping("/update-listing-status")
    @LogRecord(type = LogRecordType.ELITE_COURSE_TYPE, subType = "更新精品课上架状态", bizNo = "{{#id}}", success = "{{#listingStatusText}}精品课：【{{#course.courseName}}】")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:update')")
    public CommonResult<Boolean> updateListingStatus(@RequestParam("id") Long id) {
        eliteCourseManager.updateListingStatus(id);
        return success(true);
    }

    /**
     * 修改精品课排序
     *
     * @param id   精品课id
     * @param sort 排序
     */
    @PutMapping("/sort")
    @LogRecord(type = LogRecordType.ELITE_COURSE_TYPE, subType = "修改精品课排序", bizNo = "{{#id}}", success = "修改精品课排序：【{{#course.courseName}}】从第{{#oldSort}}位调整到第{{#sort}}位")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:update')")
    public CommonResult<Boolean> updateSort(@RequestParam("id") Long id, @RequestParam("sort") Integer sort) {
        eliteCourseManager.updateSort(id, sort);
        return success(true);
    }

    /**
     * 导出精品课数据
     */
    @LogRecord(type = LogRecordType.ELITE_COURSE_TYPE, subType = "导出精品课", bizNo = "{{#taskId}}", success = "导出精品课：创建异步导出任务")
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:export')")
    public CommonResult<Long> exportEliteCourse(
            @RequestBody @Valid EliteCourseExportReqVO exportReqVO) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 将导出请求参数和任务名称一起传递
            ExportTaskParams taskParams = new ExportTaskParams();
            taskParams.setTaskName(exportReqVO.getTaskName());
            taskParams.setQueryParams(exportReqVO);

            String params = objectMapper.writeValueAsString(taskParams);
            Long taskId = exportTaskApi.createExportTask(exportReqVO.getTaskName(),
                    ExportTaskTypeEnum.ELITE_COURSE, params);
            LogRecordContext.putVariable("taskId", taskId);
            return success(taskId);
        } catch (Exception e) {
            log.error("创建精品课导出任务失败", e);
            return CommonResult.error(500, "创建导出任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取课程概览
     *
     * @param courseId 精品课id
     */
    @GetMapping("/overview")
    @PreAuthorize("@ss.hasPermission('edu:elite-course:query')")
    public CommonResult<EliteCourseOverviewRespVO> getCourseOverview(@RequestParam("courseId") Long courseId) {
        return success(eliteCourseManager.getCourseOverview(courseId));
    }

}