<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.edu.dal.mysql.elitecourse.EliteCourseRegisterMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <sql id="commonCourseUserConditions">
        <!-- 搜索未过期 -->
        <if test="req.learningStatus != null and req.learningStatus == 1">
            <!-- 如果课程按截止日期 -->
            <if test="req.learningValidityPeriod != null and req.learningValidityPeriod == 2">
                <if test="req.deadline != null">
                    AND cr.enrollment_time &lt; #{req.deadline}
                </if>
            </if>
            <!-- 如果课程按天数 -->
            <if test="req.learningValidityPeriod != null and req.learningValidityPeriod == 3">
                <if test="req.effectiveDays != null">
                    AND now() &lt; DATE_ADD(cr.enrollment_time, INTERVAL #{req.effectiveDays} DAY)
                </if>
            </if>
        </if>
        <!-- 搜索已过期 -->
        <if test="req.learningStatus != null and req.learningStatus == 2">
            <!-- 如果课程按截止日期 -->
            <if test="req.learningValidityPeriod != null and req.learningValidityPeriod == 2">
                <if test="req.deadline != null">
                    AND cr.enrollment_time > #{req.deadline}
                </if>
            </if>
            <!-- 如果课程按天数 -->
            <if test="req.learningValidityPeriod != null and req.learningValidityPeriod == 3">
                <if test="req.effectiveDays != null">
                    AND now() > DATE_ADD(cr.enrollment_time, INTERVAL #{req.effectiveDays} DAY)
                </if>
            </if>
        </if>
        <!-- 报名途径 -->
        <if test="req.registerType != null">
            AND cr.register_type = #{req.registerType}
        </if>
        <!-- 昵称 -->
        <if test="req.nickname != null and req.nickname != ''">
            AND u.nickname LIKE CONCAT('%', #{req.nickname}, '%')
        </if>
        <!-- 手机号 -->
        <if test="req.mobile != null and req.mobile != ''">
            AND u.mobile LIKE CONCAT('%', #{req.mobile}, '%')
        </if>
        <if test="req.ids != null and req.ids.size() > 0">
            AND cr.id IN
            <foreach item="item" collection="req.ids" separator="," close=")" open="(" index="">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="commonCourseRegisterUserConditions">
        <!-- 昵称 -->
        <if test="req.nickname != null and req.nickname != ''">
            AND u.nickname LIKE CONCAT('%', #{req.nickname}, '%')
        </if>
        <!-- 手机号 -->
        <if test="req.mobile != null and req.mobile != ''">
            AND u.mobile LIKE CONCAT('%',
            #{req.mobile}, '%')
        </if>
        <if test="req.ids != null and req.ids.size() > 0">
            AND cr.id IN
            <foreach item="item" collection="req.ids" separator="," close=")" open="(" index="">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 分页获取课程用户 -->
    <select id="getCourseUserPage"
            resultType="com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseUserRespVO">
        SELECT cr.id,
        cr.user_id,
        cr.course_id,
        cr.course_type,
        cr.register_type,
        cr.enrollment_time,
        u.country_code,
        u.nickname,
        u.mobile
        FROM `edu_elite_course_register` cr
        LEFT JOIN `user` u ON cr.user_id = u.id
        WHERE cr.course_id = #{req.courseId}
        AND cr.deleted = 0
        AND u.deleted = 0
        <include refid="commonCourseUserConditions"/>
        ORDER BY cr.id DESC
    </select>

    <select id="countCourseUser" resultType="java.lang.Long">
        SELECT count(*)
        FROM `edu_elite_course_register` cr
        LEFT JOIN `user` u ON cr.user_id = u.id
        WHERE cr.course_id = #{req.courseId}
        AND cr.deleted = 0
        AND u.deleted = 0
        <include refid="commonCourseUserConditions"/>
    </select>

    <select id="getCourseRegisterUserPage"
            resultType="com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseRegisterUserRespVO">
        SELECT cr.id AS course_register_id,
        cr.course_id,
        u.id AS user_id,
        u.nickname,
        u.country_code,
        u.mobile
        FROM edu_elite_course_register cr
        LEFT JOIN `user` u ON cr.user_id = u.id
        WHERE cr.deleted = 0
        AND u.deleted = 0
        AND cr.course_id = #{req.courseId}
        <include refid="commonCourseRegisterUserConditions"/>
        ORDER BY cr.id DESC
    </select>

    <select id="countCourseRegisterUser" resultType="java.lang.Long">
        SELECT count(*)
        FROM edu_elite_course_register cr
        LEFT JOIN `user` u ON cr.user_id = u.id
        WHERE cr.deleted = 0
        AND u.deleted = 0
        AND cr.course_id = #{req.courseId}
        <include refid="commonCourseRegisterUserConditions"/>
    </select>
    <select id="getUserCourseRegisterCount" resultType="java.lang.Long">
        SELECT COUNT(r.course_id) AS valid_course_count
        FROM edu_elite_course_register r
                 JOIN edu_elite_course c ON r.course_id = c.id
        WHERE r.user_id = #{userId}
          AND r.deleted = false
          AND c.deleted = false
          AND (
            c.learning_validity_period = 1
                OR (c.learning_validity_period = 2 AND c.deadline >= NOW())
                OR
            (c.learning_validity_period = 3 AND DATE_ADD(r.enrollment_time, INTERVAL c.effective_days DAY) >= NOW())
            )
    </select>
    <select id="getUserCourseRegister"
            resultType="com.xt.hsk.module.edu.manager.elitecourse.dto.CourseAndRegisterDto">
        SELECT eecr.*,
               eec.hsk_level,
               eec.primary_category_id,
               eec.secondary_category_id,
               eec.course_name_cn,
               eec.course_name_en,
               eec.course_name_ot,
               eec.cover_url_large,
               eec.cover_url_small,
               eec.class_hour_number_status,
               eec.custom_class_hour_number,
               eec.original_price_cn,
               eec.selling_price_cn,
               eec.original_price_en,
               eec.selling_price_en,
               eec.original_price_ot,
               eec.selling_price_ot,
               eec.course_detail,
               eec.listing_method,
               eec.listing_status,
               eec.listing_time,
               eec.sales_base,
               eec.deadline,
               eec.effective_days,
               eec.effect_model,
               eec.sort,
               eec.is_show,
               eec.enrollment_count,
               eec.type,
               eec.creator
        FROM edu_elite_course_register eecr
                 left join edu_elite_course eec on eecr.course_id = eec.id
        WHERE eecr.user_id = #{req.userId}
          AND eec.hsk_level = #{req.hskLevel}
          AND eecr.deleted = 0
          AND eec.deleted = 0
        order by eecr.enrollment_time desc
    </select>
</mapper>