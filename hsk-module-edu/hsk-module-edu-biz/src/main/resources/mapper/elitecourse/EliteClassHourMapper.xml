<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.edu.dal.mysql.elitecourse.EliteClassHourMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getEliteClassHourCount"
            resultType="com.xt.hsk.module.edu.manager.elitecourse.dto.CourseHourCountDto">
        SELECT course_id, COUNT(*) AS class_hour_count
        FROM edu_elite_class_hour
        WHERE deleted = 0
        AND course_id IN
        <foreach item="item" index="index" collection="courseIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY course_id
    </select>
</mapper>