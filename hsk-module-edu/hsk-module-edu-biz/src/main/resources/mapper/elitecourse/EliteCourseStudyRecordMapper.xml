<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.edu.dal.mysql.elitecourse.EliteCourseStudyRecordMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getUserMaxStudyRecord"
            resultType="com.xt.hsk.module.edu.dal.dataobject.elitecourse.EliteCourseStudyRecordDO">
        SELECT *
        FROM (SELECT *,
        ROW_NUMBER() OVER (PARTITION BY class_hour_id ORDER BY play_length DESC) as rn
        FROM edu_elite_course_study_record
        WHERE deleted = 0
        AND user_id = #{userId}
        AND class_hour_id IN
        <foreach collection="hourIds" item="hourId" open="(" separator="," close=")">
            #{hourId}
        </foreach>) t
        WHERE rn = 1

    </select>
</mapper>