<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.edu.dal.mysql.userpracticerecord.UserPracticeRecordMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getQuestionNewestPracticeRecord"
            resultType="com.xt.hsk.module.edu.controller.app.question.vo.QuestionStatisticsRespVO">
        select sum(correct_num) as right_count,
               sum(answer_time) as answer_time_count,
               sum(answer_num)  as answer_count
        from edu_user_practice_record
        where user_id = #{userId}
          and hsk_level = #{hskLevel}
          and subject = #{subject}
        and practice_mode = 1
        <if test="textbookId != null">
            and textbook_id = #{textbookId}
        </if>
        <if test="chapterId != null">
            and chapter_id = #{chapterId}
        </if>
        and record_status = 2
          and is_newest = true
          and deleted = false
    </select>
    <select id="getUserUnitSortQuestionTypeCount"
            resultType="com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO">
        select
        unit_sort,
        question_type_id as type_id,
        sum(answer_num) as exercise_count,
        sum(question_num) as question_count,
        sum(correct_num) as correct_count
        from edu_user_practice_record
        where user_id = #{userId}
        and hsk_level = #{hskLevel}
        and subject = #{subject}
        and record_status = 2
        and practice_mode = 1
        and is_newest = true
        and deleted = false
        <if test="unitSort != null">
            and unit_sort = #{unitSort}
        </if>
        <if test="typeId != null ">
            and question_type_id = #{typeId}
        </if>
        <if test="textbookId != null">
            and textbook_id = #{textbookId}
        </if>
        <if test="chapterId != null">
            and chapter_id = #{chapterId}
        </if>
        group by unit_sort, type_id
    </select>
    <select id="getUserTextbookChapterQuestions"
            resultType="com.xt.hsk.module.edu.controller.app.question.vo.TextbookChapterQuestionRespVO">
        select
        id as practice_record_id,
        textbook_id,
        chapter_id,
        sum(answer_num) as exercise_count,
        sum(question_num) as question_count,
        sum(correct_num) as correct_count
        from edu_user_practice_record
        where user_id = #{userId}
        and hsk_level = #{hskLevel}
        and subject = #{subject}
        and is_newest = true
        and deleted = false
        and practice_mode = 1
        <if test="unitSort != null">
            and unit_sort = #{unitSort}
        </if>
        <if test="typeId != null">
            and question_type_id = #{typeId}
        </if>
        <if test="status != null and status.size() > 0 and !status.contains(3)">
            and record_status in
            <foreach item="item" collection="status" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by id,unit_sort, question_type_id
    </select>
    <select id="getUserNotFinishedPracticeRecord"
            resultType="com.xt.hsk.module.edu.controller.app.question.vo.AppUserPracticeRecordRespVO">
        select *
        from edu_user_practice_record
        where user_id = #{userId}
          and hsk_level = #{hskLevel}
          and subject = #{subject}
          and unit_sort = #{unitSort}
          and question_type_id = #{typeId}
          and textbook_id = #{textbookId}
          and chapter_id = #{chapterId}
          and record_status = 1
          and practice_mode = 1
          and deleted = false
          and is_newest = true
        order by create_time desc limit 1
    </select>
    <select id="getUserPracticeTextbookChapterList"
            resultType="com.xt.hsk.module.edu.controller.app.question.vo.TextbookChapterQuestionRespVO">
        select textbook_id,
               chapter_id,
               sum(answer_num)   as exercise_count,
               sum(question_num) as question_count,
               sum(correct_num)  as correct_count
        from (select *,
                     ROW_NUMBER() OVER (
               PARTITION BY hsk_level, subject, textbook_id, chapter_id, unit_sort, question_type_id
               ORDER BY id DESC
           ) as rn
              from edu_user_practice_record
              where user_id = #{userId}
                and hsk_level = #{hskLevel}
                and subject = #{subject}
                and record_status = 2
                and deleted = false
                and practice_mode = 1) ranked
        where rn = 1
        group by textbook_id, chapter_id
    </select>
    <select id="getQuestionNewestHaveReportPracticeRecord"
            resultType="com.xt.hsk.module.edu.controller.app.question.vo.QuestionStatisticsRespVO">

        select
        sum(correct_num) as right_count,
        sum(answer_time) as answer_time_count,
        sum(answer_num) as answer_count
        from (
        select *,
        ROW_NUMBER() OVER (
        PARTITION BY hsk_level, subject, textbook_id, chapter_id, unit_sort, question_type_id
        ORDER BY id DESC
        ) as rn
        from edu_user_practice_record
        where user_id = #{userId}
        and hsk_level = #{hskLevel}
        and subject = #{subject}
        and record_status = 2
        and deleted = false
        and practice_mode = 1
        <if test="textbookId != null">
            and textbook_id = #{textbookId}
        </if>
        <if test="chapterId != null">
            and chapter_id = #{chapterId}
        </if>) ranked
        where rn = 1
    </select>
    <select id="getHistoryUserUnitSortQuestionTypeCount"
            resultType="com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO">
        select
        id as practice_record_id,
        unit_sort,
        question_type_id as type_id,
        sum(answer_num) as exercise_count,
        sum(question_num) as question_count,
        sum(correct_num) as correct_count
        from (
        select *,
        ROW_NUMBER() OVER (
        PARTITION BY hsk_level, subject, textbook_id, chapter_id, unit_sort, question_type_id
        ORDER BY id DESC
        ) as rn
        from edu_user_practice_record
        where user_id = #{userId}
        and hsk_level = #{hskLevel}
        and subject = #{subject}
        and record_status = 2
        and practice_mode = 1
        and deleted = false
        <if test="textbookId != null">
            and textbook_id = #{textbookId}
        </if>
        <if test="chapterId != null">
            and chapter_id = #{chapterId}
        </if>
        ) ranked
        where rn = 1
        group by id,unit_sort,question_type_id
    </select>

</mapper>