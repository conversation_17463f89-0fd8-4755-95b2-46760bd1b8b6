<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.edu.dal.mysql.chapter.ChapterMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectChapterRespVOPage"
            resultType="com.xt.hsk.module.edu.controller.admin.textbook.vo.ChapterRespVO">
        select ec.*, eu.unit_name_cn as unit_name, eu.subject as unit_subject, eu.id as unit_id
        from edu_unit eu
        left join edu_chapter ec on eu.chapter_id = ec.id
        where eu.deleted = 0 and ec.deleted = 0 and eu.textbook_id = #{reqVO.textbookId}
        <if test="reqVO.chapterNameCn != null">
            and ec.chapter_name_cn like concat('%',#{reqVO.chapterNameCn},'%')
        </if>
        order by ec.chapter_order,eu.subject,eu.sort
    </select>
</mapper>