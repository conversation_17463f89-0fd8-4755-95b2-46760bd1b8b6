<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.edu.dal.mysql.interactivecourse.InteractiveCourseRecordMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 批量查询用户视频完成记录的最高资源版本号 -->
    <select id="selectMaxResourceVersionByUnitIds" resultType="com.xt.hsk.module.edu.dal.dataobject.interactivecourse.UserInteractiveCourseRecordDO">
        SELECT
            unit_id,
            MAX(resource_version) as resource_version
        FROM edu_user_interactive_course_record
        WHERE user_id = #{userId}
          AND unit_id IN
          <foreach collection="unitIds" item="unitId" open="(" separator="," close=")">
              #{unitId}
          </foreach>
          AND biz_type = 1
          AND status = 2
          AND deleted = 0
        GROUP BY unit_id
    </select>

</mapper>
