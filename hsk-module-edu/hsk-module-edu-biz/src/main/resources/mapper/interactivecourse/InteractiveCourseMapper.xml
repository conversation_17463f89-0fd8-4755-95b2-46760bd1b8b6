<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.edu.dal.mysql.interactivecourse.InteractiveCourseMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="isQuoteWord" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM edu_interactive_course_unit eicu
                 left join edu_interactive_course_unit_resource_rel eicur on eicu.id = eicur.unit_id
        WHERE eicu.question_source = 1
          AND eicur.resource_type = 4
          AND eicur.resource_id = #{wordId}
          AND eicu.deleted = 0
          AND eicur.deleted = 0

    </select>
    <!--
        根据资源 ID 列表查询每个资源与课程的唯一关联关系（课程 ID + 资源 ID 的组合），
        并返回课程基础信息（每组只保留一条记录）
    -->
    <select id="listByResourceIdList"
            resultType="com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseBaseInfoRespVO">
        SELECT t.id,
        t.type,
        t.course_name_cn,
        t.course_name_en,
        t.course_name_ot,
        t.resource_id
        FROM (
        SELECT ic.id,
        ic.type,
        ic.course_name_cn,
        ic.course_name_en,
        ic.course_name_ot,
        icur.resource_id,
        ROW_NUMBER() OVER (PARTITION BY ic.id, icur.resource_id ORDER BY icur.id) AS rn
        FROM edu_interactive_course ic
        LEFT JOIN edu_interactive_course_unit icu ON ic.id = icu.course_id
        LEFT JOIN edu_interactive_course_unit_resource_rel icur ON icu.id = icur.unit_id
        WHERE ic.deleted = 0
        AND icu.deleted = 0
        AND icur.deleted = 0
        AND icu.question_source = 2
        AND icur.resource_type = 2
        AND icur.resource_id IN
        <foreach item="resourceId" collection="resourceIdList" separator="," close=")" open="(" index="index">
            #{resourceId}
        </foreach>
        ) t
        WHERE t.rn = 1
    </select>

    <select id="listResourceIdByCourseName" resultType="java.lang.Long">
        SELECT DISTINCT icur.resource_id
        FROM `edu_interactive_course` ic
                 LEFT JOIN edu_interactive_course_unit icu ON ic.id = icu.course_id
                 LEFT JOIN edu_interactive_course_unit_resource_rel icur ON icu.id = icur.unit_id
        WHERE ic.deleted = 0
          AND icu.deleted = 0
          AND icur.deleted = 0
          AND icu.question_source = 2
          AND icur.resource_type = 2
          AND ic.course_name_cn LIKE CONCAT('%', #{courseName}, '%')
    </select>
    <select id="getInteractiveCourseQuoteByWordId"
            resultType="com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitQuoteVO">
        SELECT eic.hsk_level,
               eic.course_name_cn AS course_name,
               eicu.course_id     AS course_id,
               eicu.unit_name_cn  as unit_name,
               eicu.id,
               eicu.display_status
        FROM edu_interactive_course_unit eicu
                 left join edu_interactive_course_unit_resource_rel eicur on eicu.id = eicur.unit_id
                 inner join edu_interactive_course eic on eicu.course_id = eic.id
        WHERE eicu.question_source = 1
          AND eicur.resource_type = 4
          AND eicu.deleted = 0
          AND eicur.deleted = 0
          AND eicur.resource_id = #{wordId}
    </select>
    <select id="countQuestionQuote" resultType="java.lang.Long">
        SELECT count(distinct eic.id)
        FROM edu_interactive_course_unit eicu
        left join edu_interactive_course_unit_resource_rel eicur on eicu.id = eicur.unit_id
        inner join edu_interactive_course eic on eicu.course_id = eic.id
        WHERE eicu.question_source = 3
        AND eicur.resource_type = 3
        AND eicu.deleted = 0
        AND eicur.deleted = 0
        AND eicur.resource_id in
        <foreach item="questionId" collection="questionIds" separator="," close=")" open="(" index="index">
            #{questionId}
        </foreach>
    </select>
    <select id="getInteractiveCourseQuestionQuoteCount"
            resultType="com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRefCountDto">
        SELECT resource_id as question_id,count(distinct eic.id) as reference_count
        FROM edu_interactive_course_unit eicu
        left join edu_interactive_course_unit_resource_rel eicur on eicu.id = eicur.unit_id
        inner join edu_interactive_course eic on eicu.course_id = eic.id
        WHERE eicu.question_source = 3
        AND eicur.resource_type = 3
        AND eicu.deleted = 0
        AND eicur.deleted = 0
        AND eicur.resource_id in
        <foreach item="questionId" collection="questionIds" separator="," close=")" open="(" index="index">
            #{questionId}
        </foreach>
        GROUP BY eicur.resource_id
    </select>
</mapper>