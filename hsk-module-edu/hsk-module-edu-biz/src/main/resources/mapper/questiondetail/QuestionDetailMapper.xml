<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.edu.dal.mysql.questiondetail.QuestionDetailMapper">

    <select id="queryQuestionPage"
            resultType="com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRespVO">
        SELECT
        q.id,
        q.subject,
        q.status,
        q.updater ,
        q.type_id AS typeId,
        q.create_time AS createTime,
        q.update_time AS updateTime,
        q.deleted,
        q.textbook_id AS textbookId,
        q.unit_id AS unitId,
        q.id AS id,
        q.version AS version,
        t.type AS textbookType,
        t.name_cn AS bookNameCn,
        c.chapter_name_cn AS chapterNameCn,
        u.unit_name_cn AS unitNameCn,
        qt.name_cn AS questionTypeNameCn
        FROM edu_question_detail q
        left join edu_chapter c on c.id = q.chapter_id
        left join edu_unit u on u.id = q.unit_id
        left join edu_textbook t on t.id = q.textbook_id
        left join edu_question_type qt on qt.id = q.type_id
        WHERE q.deleted = 0
        <if test="req.textbookId != null">
            AND q.textbook_id = #{pageReqVO.textbookId}
        </if>
        <if test="req.unitId != null">
            AND q.unit_id = #{pageReqVO.unitId}
        </if>
        <if test="req.questionId != null">
            AND q.question_id = #{pageReqVO.questionId}
        </if>
        <if test="req.typeId != null">
            AND q.type_id = #{pageReqVO.typeId}
        </if>
        <if test="req.hskLevel != null">
            AND q.hsk_level = #{pageReqVO.hskLevel}
        </if>
        <if test="req.subject != null">
            AND q.subject = #{pageReqVO.subject}
        </if>
        <if test="req.status != null">
            AND q.status = #{pageReqVO.status}
        </if>
        <if test="req.isShow != null">
            AND q.is_show = #{pageReqVO.isShow}
        </if>
    </select>

</mapper>