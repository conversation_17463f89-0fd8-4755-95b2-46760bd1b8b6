<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.edu.dal.mysql.exam.ExamPaperRuleDetailMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getPaperRuleQuestionType"
            resultType="com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleQuestionTypeRespVO">
        SELECT prd.id,
               prd.paper_rule_id,
               prd.exam_question_type_id,
               prd.question_count,
               qt.`subject`,
               qt.unit,
               qt.question_type_ids
        FROM edu_exam_paper_rule_detail prd
                 LEFT JOIN edu_exam_question_type qt ON prd.exam_question_type_id = qt.id
        WHERE prd.deleted = 0
          AND qt.deleted = 0
          AND prd.paper_rule_id = #{paperRuleId}
        ORDER BY qt.`subject`,
                 qt.unit
    </select>
</mapper>