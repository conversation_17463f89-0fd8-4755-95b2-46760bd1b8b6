<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.edu.dal.mysql.exam.ExamDetailMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="countQuestionQuote" resultType="java.lang.Long">
        <![CDATA[
        SELECT
            COUNT(*)
        FROM
            edu_exam_detail,
            JSON_TABLE(questions, '$[*]' COLUMNS (
                questionId INT PATH '$.questionId'
            )) AS jt
        WHERE
            questionId IN
     ]]>
        <foreach collection="questionIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getExamQuestionQuoteCount"
            resultType="com.xt.hsk.module.edu.controller.admin.question.vo.QuestionRefCountDto">
        SELECT
        questionId AS question_id,
        COUNT(*) as reference_count
        FROM
        edu_exam_detail,
        JSON_TABLE(questions, '$[*]' COLUMNS (
        questionId INT PATH '$.questionId'
        )) AS jt
        WHERE
        questionId IN
        <foreach collection="questionIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        questionId
    </select>
</mapper>