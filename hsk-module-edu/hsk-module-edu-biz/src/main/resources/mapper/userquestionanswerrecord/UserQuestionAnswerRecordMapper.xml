<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.edu.dal.mysql.userquestionanswerrecord.UserQuestionAnswerRecordMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getUserUnitSortQuestionTypeCount"
            resultType="com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO">
        select euqar.question_type_id as type_id,
               eu.sort                as unit_sort,
        count(1) as exercise_count,
               sum(euqad.is_correct)  as correct_count
        from edu_user_question_answer_record euqar
                 inner join edu_unit eu on euqar.unit_id = eu.id and eu.deleted = 0
                 left join edu_user_question_answer_data euqad on euqar.id = euqad.record_id and euqad.deleted = 0
        inner join edu_user_practice_record ep on euqar.practice_id = ep.id
        where euqar.deleted = 0
          and euqar.record_status = 2
        and euqar.user_id = #{reqVo.userId}
        and eu.hsk_level = #{reqVo.hskLevel}
        and eu.subject = #{reqVo.subject}
        and ep.deleted = 0
        and ep.is_newest = ture
        <if test="reqVo.typeId != null ">
            and euqar.question_type_id = #{reqVo.typeId}
        </if>
        <if test="reqVo.unitSort != null ">
            and eu.sort = #{reqVo.unitSort}
        </if>
        group by euqar.question_type_id, eu.sort
    </select>
    <select id="getUserPracticeQuestionCount" resultType="java.lang.Long">
        select count(distinct question_id)
        from edu_user_question_answer_record euqar
        where euqar.deleted = 0
          and euqar.record_status != 1
          and euqar.user_id = #{userId}
    </select>
</mapper>