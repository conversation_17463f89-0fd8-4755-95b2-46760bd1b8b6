<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.edu.dal.mysql.unit.UnitMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="queryUnitUnitData"
            resultType="com.xt.hsk.module.edu.controller.admin.textbook.vo.UnitTextBookVO">
        select un.id as id,un.unit_name_cn as unitNamecn,ec.id as chapterId,ec.chapter_name_cn as chapterNameCn,et.id as textbookId,et.name_cn as textBookNameCn
               from edu_unit  un
                           left join edu_chapter  ec on un.chapter_id=ec.id
                           LEFT JOIN edu_textbook et on un.textbook_id = et.id
               where un.deleted=0 and ec.deleted=0 and et.deleted=0
               and un.unit_name_cn = #{unitNameCn}
               and ec.chapter_name_cn = #{chapterNameCn}
               and et.name_cn = #{textBookNameCn}
               and un.subject = #{subject}
    </select>

</mapper>