# 运行时阶段
FROM eclipse-temurin:17-jdk-jammy

# 设置时区和语言环境
ENV TZ=Asia/Shanghai
ENV LANG=zh_CN.UTF-8
ENV LANGUAGE=zh_CN:zh
ENV LC_ALL=zh_CN.UTF-8
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

WORKDIR /app

# 复制构建产物
COPY hsk-server/target/hsk-server.jar ./
COPY hsk-server/src/main/resources ./config/

# 启动命令
ENTRYPOINT ["java", \
    "-Duser.language=zh", \
    "-Duser.country=CN", \
    "-Dfile.encoding=UTF-8", \
    "-Dspring.messages.fallback-to-system-locale=false", \
    "-Dlogging.config=./config/logback-spring.xml", \
    "-jar", \
    "hsk-server.jar", \
    "--spring.config.location=./config/"]
