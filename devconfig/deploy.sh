#!/bin/bash

# 修改APP_NAME为云效上的应用名
# 定义要进入的工作目录
WORK_DIR="/home/<USER>/xintong-hsk"

# 切换到工作目录
if [ -d "$WORK_DIR" ]; then
    cd "$WORK_DIR"
    echo "已进入工作目录: $WORK_DIR"
else
    echo "指定的工作目录 $WORK_DIR 不存在。"
    exit 1
fi


APP_NAME=hsk-server
CONTAINER_NAME=hsk-server-container  # 容器名称
IMAGE_NAME=hsk-server:1.0 # 镜像名称
APP_PORT=10040  # 应用端口
HEALTH_CHECK_URL=http://127.0.0.1:${APP_PORT}/actuator/health  # 应用健康检查URL
APP_START_TIMEOUT=20  # 等待应用启动的时间


usage() {
    echo "Usage: $0 {start|stop|restart}"
    exit 2
}

health_check() {
    exptime=0
    echo "checking ${APP_NAME}"
    while true
    do
        # 检查容器是否正在运行
        CONTAINER_STATUS=$(docker inspect -f '{{.State.Running}}' ${CONTAINER_NAME} 2>/dev/null)
        if [ "$CONTAINER_STATUS" != "true" ]; then
            echo -n -e "\rapplication not started"
        else
            # 尝试访问健康检查 URL
            RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" ${HEALTH_CHECK_URL})
            if [ "$RESPONSE" = "200" ]; then
              curl -X POST -fqs -d '{"msgtype":"text","text":{"content":"HSK后端服务测试环境发布完成"}}' -H 'Content-Type: application/json' \
  "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c111b8ea-6d30-4ef3-ac02-9d7d877be5da"
                echo "check ${APP_NAME} success"
                break
            fi
        fi
        sleep 4
        ((exptime++))

        echo -e "\rWait app to pass health check: $exptime..."

        if [ $exptime -gt ${APP_START_TIMEOUT} ]; then
            echo 'app start failed'
            exit 1
        fi
    done
}

start_application() {
    echo "starting docker container"
    echo "Current working directory: $(pwd)"

    # 构建镜像
    docker build -t ${IMAGE_NAME} .

    # 停止并删除旧容器
    docker stop ${CONTAINER_NAME} || true
    docker rm ${CONTAINER_NAME} || true

    # 运行新容器
    docker run -d \
        -p ${APP_PORT}:${APP_PORT} \
        --name ${CONTAINER_NAME} \
        -e LANG=zh_CN.UTF-8 \
        -e LANGUAGE=zh_CN:zh \
        -e LC_ALL=zh_CN.UTF-8 \
        ${IMAGE_NAME}

    echo "started docker container"
}

stop_application() {
    echo "stop docker container"
    docker stop ${CONTAINER_NAME} || true
    docker rm ${CONTAINER_NAME} || true
    echo "docker container has stopped"
}

start() {
    start_application
    health_check
}

stop() {
    stop_application
}

case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        stop
        start
        ;;
    *)
        usage
        ;;
esac
