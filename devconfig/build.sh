#!/bin/bash
set -ex

# 若依标准构建流程（多模块项目）
# 需要在父级目录执行构建
export MAVEN_OPTS="-Xmx2048m -XX:MaxMetaspaceSize=1024m"

# 定位到父pom所在目录（根据实际结构调整）
cd /app

# 先清理安装所有模块
mvn -f pom.xml clean install \
    -Dmaven.test.skip=true \
    -Dautoconfig.skip \
    -e

# 特殊处理admin模块
cd kp-admin
mvn clean package \
    -Dmaven.test.skip=true \
    -Dautoconfig.skip \
    -e

# 验证JAR文件
if [ ! -f "target/kp-admin.jar" ]; then
    echo "错误：主JAR文件未生成！"
    ls -l target/
    exit 1
fi
curl -X POST -fqs -d '{"msgtype":"text","text":{"content":"kp-admin 测试环境构建完成"}}' -H 'Content-Type: application/json' \
  "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c111b8ea-6d30-4ef3-ac02-9d7d877be5da"
