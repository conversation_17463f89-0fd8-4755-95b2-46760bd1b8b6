package com.xt.hsk.framework.security.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token 安全配置类，集成在security模块中
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(prefix = "sa-token", value = "enable", matchIfMissing = true) // 允许通过配置禁用
@Slf4j
public class SaTokenSecurityConfiguration implements WebMvcConfigurer {

    @PostConstruct
    public void init() {
        log.info("Sa-Token安全配置初始化完成");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        log.info("注册Sa-Token拦截器，拦截/app-api/**路径");
        // 注册Sa-Token的路由拦截器
        registry.addInterceptor(new SaInterceptor(handle -> StpUtil.checkLogin()))
            // 拦截的路径
            .addPathPatterns("/app-api/**")
            // 不拦截的路径
            .excludePathPatterns(
                "/app-api/sms/**",
                "/app-api/user/auth/**",
                "/app-api/captcha/**",
                "/app-api/user/sms/**",
                "/app-api/user/auth/**"
            );
    }
} 