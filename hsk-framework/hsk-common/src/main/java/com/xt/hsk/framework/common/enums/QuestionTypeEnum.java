package com.xt.hsk.framework.common.enums;

import java.util.Arrays;
import java.util.List;
import lombok.Getter;

/**
 * 真题题目类型枚举
 *
 * <AUTHOR>
 * @since 2025/08/01
 */
@Getter
public enum QuestionTypeEnum {
    /**
     * 题目类型
     */
    LISTENING_PICTURE_JUDGE(1, "听力", "图片判断题"),
    LISTENING_PICTURE_SINGLE_CHOICE(2, "听力", "图片单选题"),
    LISTENING_PICTURE_MATCH(3, "听力", "图片匹配题"),
    LISTENING_TEXT_SINGLE_CHOICE(4, "听力", "文字单选题"),
    LISTENING_COMPREHENSION(5, "听力", "材料单选题"),
    LISTENING_TEXT_JUDGE(6, "听力", "文字判断题"),

    READING_PICTURE_JUDGE(7, "阅读", "图片判断题"),
    READING_PICTURE_MATCH(8, "阅读", "图片匹配题"),
    READING_TEXT_MATCH(9, "阅读", "文字匹配题"),
    READING_WORD_COMPLETION(10, "阅读", "选词填空题"),
    READING_TEXT_JUDGE_QUESTION(11, "阅读", "文字判断题"),
    READING_TEXT_SINGLE_CHOICE(12, "阅读", "文字单选题"),
    READING_ARRANGEMENT(13, "阅读", "排列顺序题"),
    READING_COMPREHENSION(14, "阅读", "阅读理解题"),
    READING_SENTENCE_CORRECTION(15, "阅读", "句子纠错题"),
    READING_CLOZE_TEST(16, "阅读", "完型填空题"),

    WRITING_CONSTRUCT_SENTENCE(17, "书写", "连词成句题"),
    WRITING_CHINESE_CHARACTER(18, "书写", "汉字书写题"),
    WRITING_PICTURE_SENTENCE(19, "书写", "看图造句题"),
    WRITING_SUMMARY(20, "书写", "缩写文章题"),
    WRITING_ESSAY(21, "书写", "短文写作题"),

    READING_SENTENCE_CHOICE(22, "阅读", "选句填空题"),
    ;

    private final long code;
    private final String subject;
    private final String desc;

    QuestionTypeEnum(long code, String subject, String desc) {
        this.code = code;
        this.subject = subject;
        this.desc = desc;
    }

    public long getCode() {
        return code;
    }

    public String getSubject() {
        return subject;
    }

    public String getDesc() {
        return desc;
    }

    // 通过code查找枚举的辅助方法
    public static QuestionTypeEnum getByCode(long code) {
        for (QuestionTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("无效的题目类型编码: " + code);
    }

    // 获取从材料中获取选项的题型
    public static List<QuestionTypeEnum> getFromMaterialQuestionTypeList() {
        return Arrays.asList(QuestionTypeEnum.LISTENING_PICTURE_MATCH,
                READING_PICTURE_MATCH, READING_TEXT_MATCH, READING_WORD_COMPLETION, READING_SENTENCE_CHOICE);

    }

    // 需要手动打乱顺序的题型
    public static List<QuestionTypeEnum> getNeedShuffleQuestionTypeList() {
        return Arrays.asList(QuestionTypeEnum.READING_ARRANGEMENT,
                QuestionTypeEnum.WRITING_CONSTRUCT_SENTENCE);
    }

    // 需要单个小题保存的题型
    public static List<QuestionTypeEnum> getSingleQuestionTypeList() {
        return Arrays.asList(QuestionTypeEnum.LISTENING_COMPREHENSION,
                QuestionTypeEnum.READING_COMPREHENSION,
                QuestionTypeEnum.READING_CLOZE_TEST,
                QuestionTypeEnum.READING_SENTENCE_CHOICE
        );
    }

    // 获取写作题
    public static List<QuestionTypeEnum> getWritingQuestionTypeList() {
        return Arrays.asList(
//                QuestionTypeEnum.WRITING_CHINESE_CHARACTER,
                QuestionTypeEnum.WRITING_PICTURE_SENTENCE,
                QuestionTypeEnum.WRITING_SUMMARY,
                QuestionTypeEnum.WRITING_ESSAY);
    }

    /**
     * 判断是否需要AI批改(主观题)
     */
    public static boolean isNeedAICorrect(long code) {
        List<QuestionTypeEnum> questionTypeList = getWritingQuestionTypeList();
        return questionTypeList.contains(QuestionTypeEnum.getByCode(code));
    }
}