package com.xt.hsk.module.user.aop;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xt.hsk.framework.common.util.servlet.ServletUtils;
import com.xt.hsk.framework.web.core.util.WebFrameworkUtils;
import com.xt.hsk.module.user.annotation.UserOperateLog;
import com.xt.hsk.module.user.service.user.UserOperateLogService;
import jakarta.annotation.Resource;
import java.lang.reflect.Method;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 用户操作日志切面
 * <p>
 * 负责拦截带有 {@link UserOperateLog} 注解的方法，自动记录用户操作日志。
 * 支持从方法参数中获取用户信息，并在方法执行前后记录日志。
 * 同时处理方法执行异常时的日志记录。
 *
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class UserOperateLogAspect {

    @Resource
    private UserOperateLogService userOperateLogService;

    @Resource
    private ObjectMapper objectMapper;

    // SpEL表达式解析器
    private final ExpressionParser expressionParser = new SpelExpressionParser();
    // 参数名发现器，用于获取方法参数名
    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    /**
     * 定义切点，拦截带有UserOperateLog注解的方法
     */
    @Pointcut("@annotation(com.xt.hsk.module.user.annotation.UserOperateLog)")
    public void userOperateLogPointcut() {
    }

    /**
     * 环绕通知，在方法执行前后记录日志
     *
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 方法执行异常
     */
    @Around("userOperateLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        // 获取注解信息
        UserOperateLog userOperateLog = method.getAnnotation(UserOperateLog.class);

        // 创建SpEL上下文
        EvaluationContext context = createEvaluationContext(joinPoint);

        // 检查是否启用日志记录
        Boolean enable = parseSpEL(userOperateLog.enable(), context, Boolean.class);
        if (Boolean.FALSE.equals(enable)) {
            return joinPoint.proceed();
        }

        // 获取用户ID
        Long userId = parseSpEL(userOperateLog.userIdSpEL(), context, Long.class);
        if (userId == null) {
            log.warn("用户ID解析失败，无法记录日志: {}", userOperateLog.userIdSpEL());
            return joinPoint.proceed();
        }

        // 获取操作前状态
        Integer beforeStatus = null;
        if (StringUtils.hasText(userOperateLog.beforeStatusSpEL())) {
            beforeStatus = parseSpEL(userOperateLog.beforeStatusSpEL(), context, Integer.class);
        }

        // 执行原方法
        Object result = joinPoint.proceed();

        // 将方法返回结果添加到 SpEL 上下文中
        context.setVariable("result", result);

        // 方法执行成功后记录日志
        try {
            recordOperateLog(userOperateLog, context, userId, beforeStatus);
        } catch (Exception e) {
            log.error("记录用户操作日志失败", e);
        }

        return result;
    }

    /**
     * 记录用户操作日志
     */
    private void recordOperateLog(UserOperateLog userOperateLog, EvaluationContext context, 
                                  Long userId, Integer beforeStatus) {
        try {
            // 获取操作后状态
            Integer afterStatus = null;
            if (StringUtils.hasText(userOperateLog.afterStatusSpEL())) {
                afterStatus = parseSpEL(userOperateLog.afterStatusSpEL(), context, Integer.class);
            }

            // 获取操作者ID
            Long operatorId = null;
            if (StringUtils.hasText(userOperateLog.operatorIdSpEL())) {
                // 手动指定操作者ID
                operatorId = parseSpEL(userOperateLog.operatorIdSpEL(), context, Long.class);
            } else if (userOperateLog.recordOperator()) {
                // 需要记录操作人（后台管理操作）
                operatorId = WebFrameworkUtils.getLoginUserId();
            }
            // 如果recordOperator为false，则operatorId保持为null（用户端个人操作）

            // 获取备注信息
            String remark = userOperateLog.remark();
            if (StringUtils.hasText(remark) && remark.contains("#")) {
                remark = parseSpEL(remark, context, String.class);
            }

            // 获取扩展信息
            String extra = null;
            if (StringUtils.hasText(userOperateLog.extraSpEL())) {
                Object extraObj = parseSpEL(userOperateLog.extraSpEL(), context, Object.class);
                if (extraObj != null) {
                    extra = convertToJson(extraObj);
                }
            }

            // 获取操作IP
            String operateIp = ServletUtils.getClientIP();

            // 记录日志
            userOperateLogService.createUserOperateLog(
                userId,
                userOperateLog.operateType(),
                beforeStatus,
                afterStatus,
                operatorId,
                remark,
                operateIp,
                extra
            );

            log.debug("用户操作日志记录成功: userId={}, operateType={}, remark={}",
                     userId, userOperateLog.operateType(), remark);

        } catch (Exception e) {
            log.error("记录用户操作日志失败: userId={}, operateType={}",
                     userId, userOperateLog.operateType(), e);
        }
    }

    /**
     * 创建SpEL评估上下文
     */
    private EvaluationContext createEvaluationContext(ProceedingJoinPoint joinPoint) {
        StandardEvaluationContext context = new StandardEvaluationContext();
        
        // 获取方法参数名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] paramNames = parameterNameDiscoverer.getParameterNames(signature.getMethod());
        Object[] args = joinPoint.getArgs();
        
        // 将参数添加到上下文中
        if (paramNames != null) {
            for (int i = 0; i < paramNames.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }
        }
        
        return context;
    }

    /**
     * 解析SpEL表达式
     */
    private <T> T parseSpEL(String spEL, EvaluationContext context, Class<T> clazz) {
        if (!StringUtils.hasText(spEL)) {
            return null;
        }
        
        try {
            Expression expression = expressionParser.parseExpression(spEL);
            return expression.getValue(context, clazz);
        } catch (Exception e) {
            log.warn("SpEL表达式解析失败: {}", spEL, e);
            return null;
        }
    }

    /**
     * 将对象转换为JSON字符串
     */
    private String convertToJson(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.warn("对象转JSON失败", e);
            return obj.toString();
        }
    }


}
