package com.xt.hsk.module.user.manager.user;

import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.xt.hsk.framework.common.enums.CommonStatusEnum;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.util.servlet.ServletUtils;
import com.xt.hsk.framework.ip.core.utils.IPUtils;
import com.xt.hsk.module.edu.api.elitecourse.EliteCourseRegisterApi;
import com.xt.hsk.module.edu.api.exam.UserExamRecordApi;
import com.xt.hsk.module.edu.api.question.QuestionAnswerRecordApi;
import com.xt.hsk.module.system.api.sms.SmsCodeApi;
import com.xt.hsk.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import com.xt.hsk.module.system.enums.sms.SmsSceneEnum;
import com.xt.hsk.module.user.controller.app.user.vo.ChangeMobileReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.SetPasswordReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserBehaviorSaveVo;
import com.xt.hsk.module.user.controller.app.user.vo.UserCenterRespVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserLoginRespVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserSetInfoReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserUpdatePasswordReqVO;
import com.xt.hsk.module.user.convert.appuser.AppUserConvert;
import com.xt.hsk.module.user.dal.dataobject.ext.UserExtDO;
import com.xt.hsk.module.user.dal.dataobject.user.UserDO;
import com.xt.hsk.module.user.enums.ErrorCodeConstants;
import com.xt.hsk.module.user.service.user.UserExtService;
import com.xt.hsk.module.user.service.user.UserService;
import com.xt.hsk.module.user.util.CountryCodeUtils;
import jakarta.annotation.Resource;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户管理器
 *
 * <AUTHOR>
 * @since 2025/01/27
 */
@Slf4j
@Component
public class AppUserManager {

    @Resource
    private UserService userService;
    @Resource
    private QuestionAnswerRecordApi questionAnswerRecordApi;
    @Resource
    private UserExamRecordApi userExamRecordApi;
    @Resource
    private EliteCourseRegisterApi eliteCourseRegisterApi;

    @Resource
    private SmsCodeApi smsCodeApi;
    @Resource
    private UserExtService userExtService;

    /**
     * 设置密码
     */
    @Transactional(rollbackFor = Exception.class)
    public void setPassword(SetPasswordReqVO reqVO) {
        long userId = StpUtil.getLoginIdAsLong();
        UserDO user = authenticate(userId);
        userService.encodePassword(user.getId(), reqVO.getPassword());
    }

    /**
     * APP用户注销账号
     */
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(name = "logoutUser", keys = {"#userId"}, acquireTimeout = 3000, expire = 10000)
    public void logoutUser() {
        long userId = StpUtil.getLoginIdAsLong();
        // 校验用户存在
        authenticate(userId);
        // 执行注销
        userService.logoutUser(userId);
    }

    /**
     * 修改密码
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(UserUpdatePasswordReqVO reqVO) {
        long userId = StpUtil.getLoginIdAsLong();

        // 修改密码
        updatePasswordWithLock(reqVO, userId);

        // 踢下线
        StpUtil.kickout(userId);
    }

    /**
     * 修改密码（带分布式锁）
     */
    @Lock4j(name = "updatePassword", keys = {"#userId"}, acquireTimeout = 3000, expire = 10000)
    private void updatePasswordWithLock(UserUpdatePasswordReqVO reqVO, Long userId) {
        UserDO user = authenticate(userId);

        // 验证新密码和确认密码是否一致
        if (!reqVO.getNewPassword().equals(reqVO.getConfirmPassword())) {
            throw exception(ErrorCodeConstants.USER_PASSWORD_CONFIRM_NOT_MATCH);
        }

        // 验证原密码是否正确
        if (!userService.isPasswordMatch(reqVO.getOldPassword(), user.getPassword())) {
            throw exception(ErrorCodeConstants.USER_PASSWORD_ERROR);
        }

        // 验证新密码不能与原密码相同
        if (userService.isPasswordMatch(reqVO.getNewPassword(), user.getPassword())) {
            throw exception(ErrorCodeConstants.USER_PASSWORD_SAME_AS_OLD);
        }

        // 更新密码
        userService.updateUserPassword(user.getId(), reqVO.getNewPassword());
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    public UserLoginRespVO getUserInfo() {
        long userId = StpUtil.getLoginIdAsLong();
        // 校验用户
        UserDO user = authenticate(userId);
        UserLoginRespVO respVO = AppUserConvert.INSTANCE.convertToLoginResp(user);
        respVO.setToken(StpUtil.getTokenValue());
        return respVO;
    }

    /**
     * 校验用户
     */
    public UserDO authenticate(Long userId) {
        UserDO user = userService.getById(userId);
        // 校验用户是否存在
        if (user == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        // 校验是否禁用
        if (CommonStatusEnum.isDisable(user.getStatus())) {
            throw exception(ErrorCodeConstants.USER_DISABLED);
        }
        return user;
    }

    /**
     * 设置用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void setUserInfo(UserSetInfoReqVO reqVO) {
        long userId = StpUtil.getLoginIdAsLong();
        // 校验用户
        UserDO user = authenticate(userId);
        UserDO convert = AppUserConvert.INSTANCE.doToRespVO(reqVO);
        convert.setId(user.getId());

        // 防止昵称被覆盖
        if (StringUtils.isEmpty(reqVO.getNickname())) {
            convert.setNickname(user.getNickname());
        }

        // 处理是否首次更新用户信息的逻辑
        if (reqVO.getFirstUpdate() != null && reqVO.getFirstUpdate()) {
            convert.setInfoUpdated(true);
            // 第一次如果没自己选等级 自动设置 如果自己选了就用自己设置的值
            if (reqVO.getCurrentHskLevel() == null) {
                if (Objects.equals(reqVO.getChineseLevel(), 1)) {
                    convert.setCurrentHskLevel(HskEnum.HSK_1.code);
                } else if (Objects.equals(reqVO.getChineseLevel(), 2)) {
                    convert.setCurrentHskLevel(HskEnum.HSK_2.code);
                } else if (Objects.equals(reqVO.getChineseLevel(), 3)) {
                    convert.setCurrentHskLevel(HskEnum.HSK_3.code);
                }
            }
        } else if (user.getInfoUpdated() != null && user.getInfoUpdated()) {
            // 如果用户已经更新过信息，保持为true
            convert.setInfoUpdated(true);
        } else {
            // 非首次更新且之前未更新过，设置为false
            convert.setInfoUpdated(false);
        }
        userService.updateById(convert);
    }

    public void updateUserArea() {
        if (StpUtil.isLogin()) {
            UserDO userDO = authenticate(StpUtil.getLoginIdAsLong());
            String country = IPUtils.getCountry(ServletUtils.getClientIP());
            if (country != null && !country.equals(userDO.getCountry())) {
                userDO.setCountry(country);
                userService.updateById(userDO);
            }
        }
    }

    public UserCenterRespVO getUserCenterInfo() {
        UserCenterRespVO respVO = new UserCenterRespVO();
        long userId = StpUtil.getLoginIdAsLong();
        // 获取用户信息
        UserDO userDO = authenticate(userId);
        respVO.setNickname(userDO.getNickname());
        respVO.setAvatar(userDO.getAvatar());
        respVO.setCurrentLevel(userDO.getCurrentHskLevel());
        respVO.setPlannedExamDate(userDO.getPlannedExamDate());
        // 设置考试天数
//        DateUtil.betweenDay(userDO.getExamDate(), DateUtil.date());
        // 获取用户学习信息
        Long questionCount = questionAnswerRecordApi.getUserPracticeQuestionCount(userId);
        respVO.setStudiedQuestions(questionCount.intValue());
        // 获取用户模考次数
        Long recordCount = userExamRecordApi.getUserExamRecordCount(userId);
        respVO.setMockExamCount(recordCount.intValue());
        // 用户用户课程数
        Long userCourseRegisterCount = eliteCourseRegisterApi.getUserCourseRegisterCount(userId);
        respVO.setMyCourseCount(userCourseRegisterCount.intValue());
        // 获取用户学习时长
        UserExtDO userExtDO = userExtService.getById(userId);
        Long studyTime = 0L;
        if (userExtDO != null) {
            studyTime = userExtDO.getStudyTime();
        }
        respVO.setStudiedSeconds(studyTime);


        return respVO;
    }

    public Long recordUserBehavior(UserBehaviorSaveVo reqVO) {
        Long userId = StpUtil.getLoginIdAsLong();
        UserExtDO userExtDO = userExtService.getById(userId);
        if (userExtDO == null) {
            UserExtDO saveDo = new UserExtDO();
            saveDo.setId(userId);
            saveDo.setStudyTime(reqVO.getStudyTime());
            userExtService.save(saveDo);
        } else {
            userExtDO.setStudyTime((userExtDO.getStudyTime() != null ? userExtDO.getStudyTime() : 0) + reqVO.getStudyTime());
            userExtService.updateById(userExtDO);
        }

        return userId;
    }

}
