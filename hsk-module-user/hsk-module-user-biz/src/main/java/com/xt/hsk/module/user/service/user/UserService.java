package com.xt.hsk.module.user.service.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.user.controller.admin.user.vo.UserImportExcelVO;
import com.xt.hsk.module.user.controller.admin.user.vo.UserPageReqVO;
import com.xt.hsk.module.user.dal.dataobject.user.UserDO;
import com.xt.hsk.module.user.enums.UserSourceEnum;
import java.util.List;
import java.util.Set;

/**
 * 用户 Service 接口
 *
 * <AUTHOR>
 */
public interface UserService extends IService<UserDO> {

    /**
     * 获得用户分页
     *
     * @param pageReqVO 分页查询
     * @return 用户分页
     */
    PageResult<UserDO> getUserPage(UserPageReqVO pageReqVO);

    /**
     * 根据手机号获取用户
     *
     * @param countryCode 国家区号
     * @param phone       手机号
     * @return 用户
     */
    UserDO getUserByPhone(String countryCode, String phone);

    /**
     * 密码是否匹配
     *
     * @param password     客户端传递过来的密码
     * @param userPassword 用户真实的密码
     * @return boolean
     */
    boolean isPasswordMatch(String password, String userPassword);

    /**
     * 更新用户登录信息
     *
     * @param userId   用户ID
     * @param clientIP 客户端IP
     */
    void updateUserLogin(Long userId, String clientIP);

    /**
     * 创建用户
     *
     * @param countryCode    区号
     * @param mobile         手机号码
     * @param userSourceEnum 用户注册来源
     */
    UserDO createUser(String countryCode, String mobile, UserSourceEnum userSourceEnum);

    /**
     * 密码加密存储
     *
     * @param id       用户ID
     * @param password 密码
     */
    void encodePassword(Long id, String password);

    /**
     * 更新用户密码
     *
     * @param id          身份证
     * @param newPassword 新密码
     */
    void updateUserPassword(Long id, String newPassword);

    /**
     * 系统管理员创建用户
     */
    UserDO createUserByAdmin(String countryCode, String mobile, String nickname);

    /**
     * 重置用户密码
     */
    void resetPassword(Long id);

    /**
     * 管理员删除用户
     */
    void adminDeleteUser(Long id);

    /**
     * 修改用户状态
     */
    void updateUserStatus(Long id);

    /**
     * APP用户注销账号
     *
     * @param userId 用户ID
     */
    void logoutUser(Long userId);

    /**
     * 检查指定手机号是否已存在
     *
     * @param countryCode 国家区号
     * @param mobile      手机号
     * @return 存在返回true，不存在返回false
     */
    boolean existsByPhone(String countryCode, String mobile);

    /**
     * 批量检查手机号是否已存在
     *
     * @param countryCodes 国家区号列表
     * @param mobiles      手机号列表
     * @return 已存在的手机号Set集合（格式：区号-手机号）
     */
    Set<String> batchCheckPhonesExist(List<String> countryCodes, List<String> mobiles);

    /**
     * 批量创建用户
     *
     * @param importUsers 导入的用户列表
     * @return 创建的用户ID列表
     */
    List<Long> batchCreateUsersByAdmin(List<UserImportExcelVO> importUsers);

    /**
     * 修改用户手机号
     *
     * @param userId 用户ID
     * @param newCountryCode 新区号
     * @param newMobile 新手机号
     */
    void updateUserMobile(Long userId, String newCountryCode, String newMobile);
}