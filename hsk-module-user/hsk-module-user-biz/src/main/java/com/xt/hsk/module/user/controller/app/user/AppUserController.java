package com.xt.hsk.module.user.controller.app.user;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import cn.dev33.satoken.stp.StpUtil;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.user.controller.app.user.vo.ChangeMobileReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.ChangeMobileStatusRespVO;
import com.xt.hsk.module.user.controller.app.user.vo.SetPasswordReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserLoginRespVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserSetInfoReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.UserUpdatePasswordReqVO;
import com.xt.hsk.module.user.controller.app.user.vo.VerifyOldMobileReqVO;
import com.xt.hsk.module.user.manager.changemobile.ChangeMobileManager;
import com.xt.hsk.module.user.manager.user.AppUserManager;
import com.xt.hsk.module.user.service.user.UserService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * APP端 - 用户
 *
 * <AUTHOR>
 * @since 2025/05/30
 */
@RestController
@RequestMapping("/user/")
@Validated
public class AppUserController {

    @Resource
    private AppUserManager appUserManager;

    @Resource
    private ChangeMobileManager changeMobileManager;

    @Resource
    private UserService userService;

    /**
     * 根据token获取用户信息
     *
     * @return 登录结果
     */
    @PostMapping("/getUserInfo")
    public CommonResult<UserLoginRespVO> getUserInfo() {
        return success(appUserManager.getUserInfo());
    }

    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public CommonResult<Boolean> logout() {
        StpUtil.logout();
        return success(true);
    }

    /**
     * 设置密码
     */
    @PostMapping("/setPassword")
    public CommonResult<Boolean> setPassword(@Valid @RequestBody SetPasswordReqVO reqVO) {
        appUserManager.setPassword(reqVO);
        return success(true);
    }

    /**
     * 用户注销账号
     */
    @PostMapping("/logoutUser")
    public CommonResult<Boolean> logoutUser() {
        appUserManager.logoutUser();
        return success(true);
    }

    /**
     * 设置用户信息
     */
    @PostMapping("/setUserInfo")
    public CommonResult<Boolean> setUserInfo(@Valid @RequestBody UserSetInfoReqVO reqVO) {
        appUserManager.setUserInfo(reqVO);
        return success(true);
    }

    /**
     * 更新用户地区信息
     */
    @PostMapping("/updateUserArea")
    public CommonResult<Boolean> updateUserArea() {
        appUserManager.updateUserArea();
        return success(true);
    }

    /**
     * 修改密码
     */
    @PostMapping("/updatePassword")
    public CommonResult<Boolean> updatePassword(@Valid @RequestBody UserUpdatePasswordReqVO reqVO) {
        appUserManager.updatePassword(reqVO);
        return success(true);
    }

    /**
     * 更换手机号步骤2 -> 验证旧手机号验证码
     */
    @PostMapping("/verifyOldMobile")
    public CommonResult<Boolean> verifyOldMobile(@Valid @RequestBody VerifyOldMobileReqVO reqVO) {
        return success(changeMobileManager.verifyOldMobile(reqVO));
    }

    /**
     * 更换手机号步骤4 -> 更换手机号
     */
    @PostMapping("/changeMobile")
    public CommonResult<Boolean> changeMobile(@Valid @RequestBody ChangeMobileReqVO reqVO) {
        return success(changeMobileManager.changeMobile(reqVO));
    }

    /**
     * 查询修改手机号状态
     */
    @PostMapping("/getChangeMobileStatus")
    public CommonResult<ChangeMobileStatusRespVO> getChangeMobileStatus() {
        ChangeMobileStatusRespVO status = changeMobileManager.getChangeMobileStatus();
        return success(status);
    }

    /**
     * 测试
     */
    @PostMapping("/test")
    public CommonResult<Boolean> test() {
        userService.updateUserMobile(1L, "+86", "13888888888");
        userService.updateUserPassword(1L, "abc@1234");
        return success(true);
    }
}
